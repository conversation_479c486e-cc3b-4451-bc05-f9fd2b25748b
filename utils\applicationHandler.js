const {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  ActionRowBuilder,
  ButtonBuilder,
  ButtonStyle,
  ModalBuilder,
  TextInputBuilder,
  TextInputStyle,
  StringSelectMenuBuilder,
  MessageFlags
} = require('discord.js');
const fs = require('fs');
const path = require('path');

const APPLICATION_CONFIG_FILE = path.join(__dirname, '..', 'application_config.json');
const APPLICATION_SUBMISSIONS_FILE = path.join(__dirname, '..', 'application_submissions.json');

class ApplicationHandler {
  static loadConfig(guildId) {
    try {
      if (fs.existsSync(APPLICATION_CONFIG_FILE)) {
        const data = JSON.parse(fs.readFileSync(APPLICATION_CONFIG_FILE, 'utf8'));
        return data[guildId] || null;
      }
    } catch (error) {
      console.error('Error loading application config:', error);
    }
    return null;
  }

  // Handle multi-type application start
  static async handleMultiApplicationStart(interaction) {
    const config = this.loadConfig(interaction.guildId);
    
    if (!config || !config.enabled) {
      await interaction.reply({
        content: '❌ Application system is currently disabled.',
        flags: MessageFlags.Ephemeral
      });
      return;
    }

    if (!config.applicationTypes || Object.keys(config.applicationTypes).length === 0) {
      await interaction.reply({
        content: '❌ No application types are currently available.',
        flags: MessageFlags.Ephemeral
      });
      return;
    }

    const types = Object.keys(config.applicationTypes);
    
    // If only one type exists, go directly to that application
    if (types.length === 1) {
      await this.startApplicationForType(interaction, types[0]);
      return;
    }

    // Show application type selection
    const embed = new EmbedBuilder()
      .setTitle('📋 Select Application Type')
      .setDescription('Choose which type of application you want to submit:')
      .setColor(0x3498DB);

    const selectMenu = new StringSelectMenuBuilder()
      .setCustomId('app_select_application_type')
      .setPlaceholder('Choose an application type...')
      .addOptions(
        types.map(typeId => {
          const type = config.applicationTypes[typeId];
          const isDefault = config.defaultApplicationType === typeId;
          return {
            label: type.name,
            value: typeId,
            description: type.description || `${type.questions?.length || 0} questions`,
            emoji: isDefault ? '⭐' : '📋'
          };
        })
      );

    const selectRow = new ActionRowBuilder().addComponents(selectMenu);

    await interaction.reply({
      embeds: [embed],
      components: [selectRow],
      flags: MessageFlags.Ephemeral
    });
  }

  // Handle single-type application start (direct)
  static async handleSingleApplicationStart(interaction) {
    const config = this.loadConfig(interaction.guildId);
    
    if (!config || !config.enabled) {
      await interaction.reply({
        content: '❌ Application system is currently disabled.',
        flags: MessageFlags.Ephemeral
      });
      return;
    }

    if (!config.applicationTypes || Object.keys(config.applicationTypes).length === 0) {
      await interaction.reply({
        content: '❌ No application types are currently available.',
        flags: MessageFlags.Ephemeral
      });
      return;
    }

    const types = Object.keys(config.applicationTypes);
    const defaultType = config.defaultApplicationType || types[0];
    
    // Start the application directly with the default/first type
    await this.startApplicationForType(interaction, defaultType);
  }

  // Start application for specific type
  static async startApplicationForType(interaction, typeId) {
    const config = this.loadConfig(interaction.guildId);
    const type = config.applicationTypes[typeId];

    if (!type) {
      await interaction.reply({
        content: '❌ Application type not found.',
        flags: MessageFlags.Ephemeral
      });
      return;
    }

    if (!type.questions || type.questions.length === 0) {
      await interaction.reply({
        content: '❌ This application type has no questions configured.',
        flags: MessageFlags.Ephemeral
      });
      return;
    }

    // Check if user already has a pending application
    const existingSubmission = this.getSubmission(interaction.guildId, interaction.user.id);
    if (existingSubmission && existingSubmission.status === 'submitted') {
      await interaction.reply({
        content: '❌ You already have a pending application. Please wait for it to be reviewed.',
        flags: MessageFlags.Ephemeral
      });
      return;
    }

    // Start the application process for this type
    await this.showApplicationQuestion(interaction, typeId, 0, {});
  }

  // Handle select menu interactions for application types
  static async handleSelectMenu(interaction) {
    const customId = interaction.customId;
    const selectedValue = interaction.values[0];

    try {
      if (customId === 'app_select_application_type') {
        // User selected an application type
        await this.startApplicationForType(interaction, selectedValue);
      } else if (customId.startsWith('app_choice_')) {
        // Handle choice question responses (existing functionality)
        await this.handleChoiceQuestion(interaction);
      }
    } catch (error) {
      console.error('Error handling select menu:', error);
      await interaction.reply({
        content: 'An error occurred while processing your selection.',
        flags: MessageFlags.Ephemeral
      });
    }
  }

  // Show application question for specific type
  static async showApplicationQuestion(interaction, typeId, questionIndex, answers) {
    const config = this.loadConfig(interaction.guildId);
    const type = config.applicationTypes[typeId];

    if (!type || !type.questions || questionIndex >= type.questions.length) {
      await interaction.reply({
        content: '❌ Invalid question or application type.',
        flags: MessageFlags.Ephemeral
      });
      return;
    }

    const question = type.questions[questionIndex];
    const isLastQuestion = questionIndex === type.questions.length - 1;

    const embed = new EmbedBuilder()
      .setTitle(`📋 ${type.name} - Question ${questionIndex + 1}/${type.questions.length}`)
      .setDescription(question.question)
      .setColor(0x3498DB)
      .setFooter({ 
        text: isLastQuestion ? 'This is the final question' : `${type.questions.length - questionIndex - 1} questions remaining`
      });

    if (question.type === 'choice') {
      // Handle choice questions with select menu
      const selectMenu = new StringSelectMenuBuilder()
        .setCustomId(`app_choice_${typeId}_${questionIndex}`)
        .setPlaceholder('Select your answer...')
        .addOptions(
          question.options.map((option, index) => ({
            label: option,
            value: index.toString(),
            emoji: '📝'
          }))
        );

      const selectRow = new ActionRowBuilder().addComponents(selectMenu);

      if (interaction.replied || interaction.deferred) {
        await interaction.editReply({
          embeds: [embed],
          components: [selectRow]
        });
      } else {
        await interaction.reply({
          embeds: [embed],
          components: [selectRow],
          flags: MessageFlags.Ephemeral
        });
      }
    } else {
      // Handle text questions with modal
      const modal = new ModalBuilder()
        .setCustomId(`app_question_${typeId}_${questionIndex}`)
        .setTitle(`${type.name} - Question ${questionIndex + 1}`);

      const textInput = new TextInputBuilder()
        .setCustomId('answer')
        .setLabel(question.question)
        .setStyle(question.multiline ? TextInputStyle.Paragraph : TextInputStyle.Short)
        .setRequired(question.required)
        .setMaxLength(question.maxLength || 1000);

      if (question.placeholder) {
        textInput.setPlaceholder(question.placeholder);
      }

      modal.addComponents(new ActionRowBuilder().addComponents(textInput));

      await interaction.showModal(modal);
    }
  }

  // Get user submission
  static getSubmission(guildId, userId) {
    try {
      if (fs.existsSync(APPLICATION_SUBMISSIONS_FILE)) {
        const data = JSON.parse(fs.readFileSync(APPLICATION_SUBMISSIONS_FILE, 'utf8'));
        return data[guildId]?.[userId] || null;
      }
    } catch (error) {
      console.error('Error getting submission:', error);
    }
    return null;
  }

  static saveSubmission(guildId, userId, submission) {
    try {
      let data = {};
      if (fs.existsSync(APPLICATION_SUBMISSIONS_FILE)) {
        data = JSON.parse(fs.readFileSync(APPLICATION_SUBMISSIONS_FILE, 'utf8'));
      }
      
      if (!data[guildId]) {
        data[guildId] = {};
      }
      
      data[guildId][userId] = {
        ...submission,
        timestamp: new Date().toISOString(),
        status: 'submitted'
      };
      
      fs.writeFileSync(APPLICATION_SUBMISSIONS_FILE, JSON.stringify(data, null, 2));
    } catch (error) {
      console.error('Error saving application submission:', error);
    }
  }

  static async handleApplicationStart(interaction) {
    // Use the new dynamic form handler for enhanced experience
    const DynamicFormHandler = require('./dynamicFormHandler');
    await DynamicFormHandler.startDynamicForm(interaction);
  }

  static async showQuestion(interaction, questionIndex, answers) {
    const config = this.loadConfig(interaction.guildId);
    const question = config.questions[questionIndex];
    
    if (!question) {
      // All questions answered, process the application
      await this.processApplication(interaction, answers);
      return;
    }

    if (question.type === 'text') {
      await this.showTextQuestion(interaction, questionIndex, question, answers);
    } else if (question.type === 'choice') {
      await this.showChoiceQuestion(interaction, questionIndex, question, answers);
    }
  }

  static async showTextQuestion(interaction, questionIndex, question, answers) {
    const config = this.loadConfig(interaction.guildId);
    const totalQuestions = config.questions.length;
    
    // Store current answers
    this.storeAnswers(interaction.user.id, answers);

    // Check if interaction can show modal
    if (interaction.replied || interaction.deferred) {
      // For deferred interactions, we need to show a button that will trigger the modal
      const embed = new EmbedBuilder()
        .setTitle(`📋 Application - Question ${questionIndex + 1} of ${totalQuestions}`)
        .setDescription(question.question)
        .setColor(0x3498DB)
        .addFields([
          {
            name: 'Instructions',
            value: 'Click the button below to answer this question.',
            inline: false
          },
          {
            name: 'Progress',
            value: `${questionIndex}/${totalQuestions} questions completed`,
            inline: true
          },
          {
            name: 'Required',
            value: question.required ? '✅ Yes' : '❌ No',
            inline: true
          }
        ]);

      if (question.verifyInChannels) {
        embed.addFields([
          {
            name: '🔍 Verification',
            value: `${question.verificationMode === 'required' ? '🔴 Required' : '🟡 Warning'} - Your answer will be verified against server channels`,
            inline: false
          }
        ]);
      }

      const button = new ActionRowBuilder()
        .addComponents(
          new ButtonBuilder()
            .setCustomId(`app_text_question_${questionIndex}`)
            .setLabel('Answer Question')
            .setStyle(ButtonStyle.Primary)
            .setEmoji('📝')
        );

      await interaction.editReply({
        embeds: [embed],
        components: [button]
      });
      return;
    }

    const modal = new ModalBuilder()
      .setCustomId(`app_question_${questionIndex}`)
      .setTitle(`Question ${questionIndex + 1} of ${totalQuestions}`);

    const textInput = new TextInputBuilder()
      .setCustomId('answer')
      .setLabel(question.question)
      .setStyle(question.multiline ? TextInputStyle.Paragraph : TextInputStyle.Short)
      .setRequired(question.required)
      .setMaxLength(question.maxLength || 1000);

    if (question.placeholder) {
      textInput.setPlaceholder(question.placeholder);
    }

    const actionRow = new ActionRowBuilder().addComponents(textInput);
    modal.addComponents(actionRow);

    await interaction.showModal(modal);
  }

  static async showChoiceQuestion(interaction, questionIndex, question, answers) {
    const config = this.loadConfig(interaction.guildId);
    const totalQuestions = config.questions.length;
    
    const embed = new EmbedBuilder()
      .setTitle(`📋 Application - Question ${questionIndex + 1} of ${totalQuestions}`)
      .setDescription(question.question)
      .setColor(0x3498DB)
      .addFields([
        {
          name: 'Instructions',
          value: 'Please select one of the options below.',
          inline: false
        },
        {
          name: 'Progress',
          value: `${questionIndex}/${totalQuestions} questions completed`,
          inline: true
        },
        {
          name: 'Required',
          value: question.required ? '✅ Yes' : '❌ No',
          inline: true
        }
      ]);

    const selectMenu = new StringSelectMenuBuilder()
      .setCustomId(`app_choice_${questionIndex}`)
      .setPlaceholder('Select your answer...')
      .addOptions(
        question.options.map((option, index) => ({
          label: option.length > 100 ? option.substring(0, 97) + '...' : option,
          value: option,
          description: `Option ${index + 1}`,
          emoji: '📝'
        }))
      );

    const actionRow = new ActionRowBuilder().addComponents(selectMenu);

    // Store answers for this session
    this.storeAnswers(interaction.user.id, answers);
    
    const method = interaction.replied || interaction.deferred ? 'editReply' : 'reply';
    await interaction[method]({
      embeds: [embed],
      components: [actionRow],
      flags: MessageFlags.Ephemeral
    });
  }

  static async handleModalSubmit(interaction) {
    const customId = interaction.customId;
    const questionIndex = parseInt(customId.split('_')[2]);
    const answer = interaction.fields.getTextInputValue('answer');
    
    // Validate the answer
    const config = this.loadConfig(interaction.guildId);
    const question = config.questions[questionIndex];
    
    const validationResult = await this.validateAnswer(answer, question, interaction);
    if (!validationResult.valid) {
      const errorEmbed = new EmbedBuilder()
        .setDescription(`❌ **Validation Error**\n${validationResult.error}`)
        .setColor(0xE74C3C);
      
      await interaction.reply({
        embeds: [errorEmbed],
        flags: MessageFlags.Ephemeral
      });
      return;
    }
    
    // Get stored answers
    const answers = this.getStoredAnswers(interaction.user.id) || {};
    answers[questionIndex] = answer.trim();
    this.storeAnswers(interaction.user.id, answers);

    // Check if this is the final question
    const isLastQuestion = questionIndex === config.questions.length - 1;
    
    // Show appropriate message
    let recordedEmbed;
    if (isLastQuestion) {
      recordedEmbed = new EmbedBuilder()
        .setDescription('✅ Your application has been successfully submitted. Please wait while we review your responses.')
        .setColor(0x00AE86);
    } else {
      recordedEmbed = new EmbedBuilder()
        .setDescription('✓ Answer recorded. Next question coming up...')
        .setColor(0x3498DB);
    }

    await interaction.reply({
      embeds: [recordedEmbed],
      flags: MessageFlags.Ephemeral
    });

    // No delay needed - proceed immediately to next question
    
    // Move to next question
    await this.showQuestion(interaction, questionIndex + 1, answers);
  }

  static async handleSelectMenu(interaction) {
    const customId = interaction.customId;
    const questionIndex = parseInt(customId.split('_')[2]);
    const answer = interaction.values[0];
    
    const config = this.loadConfig(interaction.guildId);
    const answers = this.getStoredAnswers(interaction.user.id) || {};
    answers[questionIndex] = answer;
    this.storeAnswers(interaction.user.id, answers);

    // Check if this is the final question
    const isLastQuestion = questionIndex === config.questions.length - 1;
    
    // Show appropriate message
    let recordedEmbed;
    if (isLastQuestion) {
      recordedEmbed = new EmbedBuilder()
        .setDescription('✅ Your application has been successfully submitted. Please wait while we review your responses.')
        .setColor(0x00AE86);
    } else {
      recordedEmbed = new EmbedBuilder()
        .setDescription('✓ Answer recorded. Next question coming up...')
        .setColor(0x3498DB);
    }

    await interaction.update({
      embeds: [recordedEmbed],
      components: []
    });

    // No delay needed - proceed immediately to next question
    
    // Move to next question
    await this.showQuestion(interaction, questionIndex + 1, answers);
  }

  static async processApplication(interaction, answers) {
    const config = this.loadConfig(interaction.guildId);
    
    // Create submission record
    const submission = {
      userId: interaction.user.id,
      username: interaction.user.tag,
      answers: answers,
      guildId: interaction.guildId
    };

    // Save submission
    this.saveSubmission(interaction.guildId, interaction.user.id, submission);

    // Add to processing queue and notify admins
    await this.addToProcessingQueue(interaction, submission, config);

    // Process the application from queue
    await this.processApplicationFromQueue(interaction, submission, config);
  }

  static async addToProcessingQueue(interaction, submission, config) {
    // Add application to queue
    const queueEntry = {
      guildId: submission.guildId,
      userId: submission.userId,
      username: submission.username,
      answers: submission.answers,
      submittedAt: new Date().toISOString(),
      status: 'queued'
    };

    // Save to queue file
    this.saveToQueue(queueEntry);

    // Notify admins about new application in queue
    await this.notifyAdminsNewApplication(interaction, submission, config);
  }

  static saveToQueue(queueEntry) {
    try {
      const queueFile = path.join(__dirname, '..', 'application_queue.json');
      let queue = [];
      
      if (fs.existsSync(queueFile)) {
        queue = JSON.parse(fs.readFileSync(queueFile, 'utf8'));
      }
      
      queue.push(queueEntry);
      fs.writeFileSync(queueFile, JSON.stringify(queue, null, 2));
    } catch (error) {
      console.error('Error saving to queue:', error);
    }
  }

  static async notifyAdminsNewApplication(interaction, submission, config) {
    try {
      // Notify via admin channel if configured
      if (config.adminChannelId) {
        const adminChannel = await interaction.guild.channels.fetch(config.adminChannelId);
        if (adminChannel) {
          const notificationEmbed = new EmbedBuilder()
            .setDescription(`📥 New application received from ${submission.username}. Added to review queue.`)
            .setColor(0x3498DB)
            .setTimestamp();

          await adminChannel.send({ embeds: [notificationEmbed] });
        }
      }

      // Also notify via log channel if different from admin channel
      if (config.logChannelId && config.logChannelId !== config.adminChannelId) {
        const logChannel = await interaction.guild.channels.fetch(config.logChannelId);
        if (logChannel) {
          const logEmbed = new EmbedBuilder()
            .setDescription(`📥 New application received from ${submission.username}. Added to review queue.`)
            .setColor(0x3498DB)
            .setTimestamp();

          await logChannel.send({ embeds: [logEmbed] });
        }
      }
    } catch (error) {
      console.error('Error notifying admins:', error);
    }
  }

  static async processApplicationFromQueue(interaction, submission, config) {
    let responseSuccess = false;
    let roleAssignmentResult = { verificationFailed: true, assignedRoles: [], failedVerifications: [] };
    
    try {
      // Process role assignments with verification
      roleAssignmentResult = await this.assignRoles(interaction, submission.answers, config);

      // Process immediately - no artificial delays needed

      // Send appropriate embed based on result
      if (roleAssignmentResult.verificationFailed) {
        // GUARANTEED RESPONSE: Send Access Update embed (denied)
        await this.sendAccessUpdateEmbedWithRetry(interaction, submission, roleAssignmentResult.failedVerifications);
        responseSuccess = true;
      } else {
        // GUARANTEED RESPONSE: Send Form Submission embed (successful)
        await this.sendFormSubmissionEmbedWithRetry(interaction, submission, config);
        responseSuccess = true;
      }

    } catch (error) {
      console.error('Critical error in processApplicationFromQueue:', error);
      
      // FAIL-SAFE: If everything fails, send a basic response
      if (!responseSuccess) {
        await this.sendFailSafeResponse(interaction, submission);
      }
    } finally {
      // Always log, update queue status and clean up
      try {
        await this.logSubmission(interaction, submission, roleAssignmentResult.assignedRoles, config);
        this.updateQueueStatus(submission.guildId, submission.userId, roleAssignmentResult.verificationFailed ? 'rejected' : 'approved');
        this.clearStoredAnswers(interaction.user.id);
      } catch (error) {
        console.error('Error in cleanup:', error);
      }
    }
  }

  static async sendFormSubmissionEmbed(interaction, submission, config) {
    const embed = new EmbedBuilder()
      .setTitle('📄 Form Submission')
      .setColor(0x00AE86);

    // Add all user responses in a clear, organized format
    const responseFields = Object.entries(submission.answers).map(([index, answer]) => {
      const question = config.questions[parseInt(index)];
      return {
        name: question.question,
        value: answer,
        inline: false
      };
    });

    embed.addFields(responseFields);

    await interaction.followUp({
      embeds: [embed],
      flags: MessageFlags.Ephemeral
    });
  }

  static async sendAccessUpdateEmbed(interaction, submission, failedVerifications) {
    // Determine the primary reason for denial
    let reason = 'Application requirements not met';
    if (failedVerifications && failedVerifications.length > 0) {
      reason = 'Background verification could not be completed';
    }

    const embed = new EmbedBuilder()
      .setTitle('🚫 Access Update')
      .setDescription('Your access request has not been approved.')
      .setColor(0xE74C3C)
      .addFields([
        {
          name: '📝 Reason',
          value: reason,
          inline: false
        },
        {
          name: '💡 Next Steps',
          value: 'If you believe this was a mistake or need clarification, please contact a staff member.',
          inline: false
        }
      ]);

    await interaction.followUp({
      embeds: [embed],
      flags: MessageFlags.Ephemeral
    });
  }

  static updateQueueStatus(guildId, userId, status) {
    try {
      const queueFile = path.join(__dirname, '..', 'application_queue.json');
      if (fs.existsSync(queueFile)) {
        let queue = JSON.parse(fs.readFileSync(queueFile, 'utf8'));
        
        // Find and update the application status
        const applicationIndex = queue.findIndex(app => app.guildId === guildId && app.userId === userId);
        if (applicationIndex !== -1) {
          queue[applicationIndex].status = status;
          queue[applicationIndex].processedAt = new Date().toISOString();
          fs.writeFileSync(queueFile, JSON.stringify(queue, null, 2));
        }
      }
    } catch (error) {
      console.error('Error updating queue status:', error);
    }
  }

  static async assignRoles(interaction, answers, config) {
    const assignedRoles = [];
    const verificationResults = [];
    const failedVerifications = [];
    
    try {
      const member = await interaction.guild.members.fetch(interaction.user.id);
      
      // First, verify all answers that require verification
      for (const [questionIndex, answer] of Object.entries(answers)) {
        const question = config.questions[parseInt(questionIndex)];
        
        if (question && question.verifyInChannels) {
          console.log(`Verifying answer for question ${questionIndex}: "${answer}"`);
          
          const verificationResult = await this.verifyUserDataInChannels(answer, question, interaction);
          
          verificationResults.push({
            questionIndex: parseInt(questionIndex),
            question: question.question,
            answer: answer,
            verified: verificationResult.valid,
            error: verificationResult.error || null
          });
          
          if (!verificationResult.valid && question.verificationMode === 'required') {
            failedVerifications.push({
              question: question.question,
              answer: answer,
              error: verificationResult.error
            });
          }
        }
      }
      
      // If any required verifications failed, don't assign any roles
      if (failedVerifications.length > 0) {
        console.log(`Role assignment blocked due to ${failedVerifications.length} failed verifications`);
        
        // Log failed verification to admin channel
        await this.logFailedVerification(interaction, failedVerifications, config);
        
        return {
          assignedRoles: [],
          verificationResults: verificationResults,
          verificationFailed: true,
          failedVerifications: failedVerifications
        };
      }
      
      // All verifications passed or no required verifications, proceed with role assignment
      for (const [questionIndex, answer] of Object.entries(answers)) {
        const roleAssignments = config.roleAssignments[questionIndex];
        if (roleAssignments) {
          let roleId = null;

          // First try exact match (for choice questions and exact text matches)
          if (roleAssignments[answer]) {
            roleId = roleAssignments[answer];
          } else {
            // For text questions, try case-insensitive matching
            const normalizedAnswer = answer.toLowerCase().trim();
            roleId = roleAssignments[normalizedAnswer];
          }

          if (roleId) {
            try {
              const role = await interaction.guild.roles.fetch(roleId);
              if (role) {
                await member.roles.add(role, 'Automatic role assignment from verified application');
                assignedRoles.push(roleId);
                console.log(`Assigned role ${role.name} to ${interaction.user.tag} for answer: "${answer}"`);
              }
            } catch (error) {
              console.error(`Error assigning role ${roleId}:`, error);
            }
          }
        }
      }
      
      // Log successful verification and role assignment
      if (verificationResults.length > 0) {
        await this.logSuccessfulVerification(interaction, verificationResults, assignedRoles, config);
      }
      
    } catch (error) {
      console.error('Error in role assignment:', error);
    }
    
    return {
      assignedRoles: assignedRoles,
      verificationResults: verificationResults,
      verificationFailed: false,
      failedVerifications: []
    };
  }

  static formatAnswers(answers, questions) {
    return Object.entries(answers)
      .map(([index, answer]) => {
        const question = questions[parseInt(index)];
        return `**${question.question}**\n${answer}`;
      })
      .join('\n\n');
  }

  static async logSubmission(interaction, submission, assignedRoles, config) {
    try {
      // Log to log channel
      if (config.logChannelId) {
        const logChannel = await interaction.guild.channels.fetch(config.logChannelId);
        if (logChannel) {
          const logEmbed = new EmbedBuilder()
            .setTitle('📋 New Application Submission')
            .setDescription(`Application received from ${interaction.user.tag}`)
            .setColor(0x3498DB)
            .addFields([
              {
                name: '👤 User',
                value: `<@${interaction.user.id}> (${interaction.user.tag})`,
                inline: true
              },
              {
                name: '⏰ Submitted',
                value: new Date().toLocaleString(),
                inline: true
              },
              {
                name: '📝 Answers',
                value: this.formatAnswers(submission.answers, config.questions),
                inline: false
              }
            ]);

          if (assignedRoles.length > 0) {
            logEmbed.addFields([
              {
                name: '🎭 Roles Assigned',
                value: assignedRoles.map(role => `<@&${role}>`).join(', '),
                inline: false
              }
            ]);
          }

          await logChannel.send({ embeds: [logEmbed] });
        }
      }

      // Log to admin channel
      if (config.adminChannelId) {
        const adminChannel = await interaction.guild.channels.fetch(config.adminChannelId);
        if (adminChannel) {
          const adminEmbed = new EmbedBuilder()
            .setTitle('🔔 Application Alert')
            .setDescription(`New application processed for ${interaction.user.tag}`)
            .setColor(0xE74C3C)
            .addFields([
              {
                name: '📊 Summary',
                value: `User: <@${interaction.user.id}>\nRoles Assigned: ${assignedRoles.length}\nTimestamp: ${new Date().toLocaleString()}`,
                inline: false
              }
            ]);

          await adminChannel.send({ embeds: [adminEmbed] });
        }
      }
    } catch (error) {
      console.error('Error logging submission:', error);
    }
  }

  // Temporary storage methods (in production, you might want to use a database)
  static tempAnswers = new Map();

  static storeAnswers(userId, answers) {
    this.tempAnswers.set(userId, answers);
  }

  static getStoredAnswers(userId) {
    return this.tempAnswers.get(userId);
  }

  static clearStoredAnswers(userId) {
    this.tempAnswers.delete(userId);
  }

  static async handleTextQuestionButton(interaction) {
    const customId = interaction.customId;
    const questionIndex = parseInt(customId.split('_')[3]);
    
    // Get stored answers and current question
    const answers = this.getStoredAnswers(interaction.user.id) || {};
    const config = this.loadConfig(interaction.guildId);
    const question = config.questions[questionIndex];
    
    if (!question) {
      await interaction.reply({
        content: '❌ Question not found. Please restart the application process.',
        flags: MessageFlags.Ephemeral
      });
      return;
    }

    const totalQuestions = config.questions.length;
    
    const modal = new ModalBuilder()
      .setCustomId(`app_question_${questionIndex}`)
      .setTitle(`Question ${questionIndex + 1} of ${totalQuestions}`);

    const textInput = new TextInputBuilder()
      .setCustomId('answer')
      .setLabel(question.question)
      .setStyle(question.multiline ? TextInputStyle.Paragraph : TextInputStyle.Short)
      .setRequired(question.required)
      .setMaxLength(question.maxLength || 1000);

    if (question.placeholder) {
      textInput.setPlaceholder(question.placeholder);
    }

    const actionRow = new ActionRowBuilder().addComponents(textInput);
    modal.addComponents(actionRow);

    await interaction.showModal(modal);
  }

  static async validateAnswer(answer, question, interaction) {
    // Basic validation
    if (question.required && (!answer || answer.trim().length === 0)) {
      return {
        valid: false,
        error: 'This question is required and cannot be empty.'
      };
    }

    if (!answer || answer.trim().length === 0) {
      return { valid: true }; // Optional question, empty is okay
    }

    const trimmedAnswer = answer.trim();

    // Length validation
    if (trimmedAnswer.length > (question.maxLength || 1000)) {
      return {
        valid: false,
        error: `Answer is too long. Maximum ${question.maxLength || 1000} characters allowed.`
      };
    }

    // Email validation (if question contains "email" in the text)
    if (question.question.toLowerCase().includes('email')) {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(trimmedAnswer)) {
        return {
          valid: false,
          error: 'Please enter a valid email address (e.g., <EMAIL>).'
        };
      }
    }

    // URL validation (if question contains "website" or "url" in the text)
    if (question.question.toLowerCase().includes('website') || question.question.toLowerCase().includes('url')) {
      try {
        new URL(trimmedAnswer);
      } catch {
        return {
          valid: false,
          error: 'Please enter a valid URL (e.g., https://example.com).'
        };
      }
    }

    // Age validation (if question contains "age" in the text)
    if (question.question.toLowerCase().includes('age')) {
      const age = parseInt(trimmedAnswer);
      if (isNaN(age) || age < 1 || age > 150) {
        return {
          valid: false,
          error: 'Please enter a valid age (1-150).'
        };
      }
    }

    // Discord username validation (if question contains "discord" or "username" in the text)
    if (question.question.toLowerCase().includes('discord') && question.question.toLowerCase().includes('username')) {
      if (trimmedAnswer.length < 2 || trimmedAnswer.length > 32) {
        return {
          valid: false,
          error: 'Discord usernames must be between 2 and 32 characters.'
        };
      }
    }

    // Channel verification system - Check if user data exists in Discord channels
    const verificationResult = await this.verifyUserDataInChannels(trimmedAnswer, question, interaction);
    if (!verificationResult.valid) {
      return verificationResult;
    }

    return { valid: true };
  }

  static async verifyUserDataInChannels(answer, question, interaction) {
    try {
      const config = this.loadConfig(interaction.guildId);
      
      // Check if verification is enabled for this question type
      if (!question.verifyInChannels) {
        return { valid: true }; // No verification required
      }

      const guild = interaction.guild;
      
      // Use specific verification channels if configured, otherwise use all accessible channels
      let channels;
      if (config.verificationChannelIds && config.verificationChannelIds.length > 0) {
        // Use specific verification channels
        channels = new Map();
        for (const channelId of config.verificationChannelIds) {
          try {
            const channel = await guild.channels.fetch(channelId);
            if (channel && channel.isTextBased() && 
                channel.permissionsFor(guild.members.me).has(['ViewChannel', 'ReadMessageHistory'])) {
              channels.set(channelId, channel);
            }
          } catch (error) {
            console.log(`Cannot access verification channel ${channelId}: ${error.message}`);
          }
        }
      } else {
        // Use all accessible channels
        channels = guild.channels.cache.filter(channel => 
          channel.isTextBased() && 
          channel.permissionsFor(guild.members.me).has(['ViewChannel', 'ReadMessageHistory'])
        );
      }

      let foundInChannel = false;
      let searchResults = [];

      // Search for the answer in recent messages across channels
      for (const [channelId, channel] of channels) {
        try {
          // Fetch recent messages (last 100 messages)
          const messages = await channel.messages.fetch({ limit: 100 });
          
          // Search for the answer in message content
          const foundMessages = messages.filter(msg => {
            const content = msg.content.toLowerCase();
            const searchTerm = answer.toLowerCase();
            
            // Different search strategies based on question type
            if (question.question.toLowerCase().includes('username') || 
                question.question.toLowerCase().includes('discord')) {
              // For usernames, look for exact matches or mentions
              return content.includes(searchTerm) || 
                     msg.author.username.toLowerCase() === searchTerm ||
                     msg.author.displayName?.toLowerCase() === searchTerm;
            } else if (question.question.toLowerCase().includes('email')) {
              // For emails, look for exact email matches
              return content.includes(searchTerm);
            } else if (question.question.toLowerCase().includes('name')) {
              // For names, look for partial matches
              return content.includes(searchTerm) || 
                     msg.author.displayName?.toLowerCase().includes(searchTerm);
            } else {
              // General text search
              return content.includes(searchTerm);
            }
          });

          if (foundMessages.size > 0) {
            foundInChannel = true;
            searchResults.push({
              channelName: channel.name,
              channelId: channel.id,
              messageCount: foundMessages.size,
              latestMessage: foundMessages.first()
            });
          }
        } catch (error) {
          // Skip channels we can't access
          console.log(`Cannot search in channel ${channel.name}: ${error.message}`);
          continue;
        }
      }

      // Verification logic based on question configuration
      if (question.verificationMode === 'required' && !foundInChannel) {
        return {
          valid: false,
          error: `❌ **Verification Failed**\nYour answer "${answer}" could not be verified in any server channels. Please ensure the information you provided is accurate and has been mentioned in the server before.`
        };
      } else if (question.verificationMode === 'warning' && !foundInChannel) {
        // Log warning but allow submission
        console.log(`Warning: Could not verify "${answer}" for user ${interaction.user.tag}`);
        
        // Send warning to admin channel if configured
        if (config.adminChannelId) {
          try {
            const adminChannel = await guild.channels.fetch(config.adminChannelId);
            if (adminChannel) {
              const warningEmbed = new EmbedBuilder()
                .setTitle('⚠️ Verification Warning')
                .setDescription(`Could not verify user response in server channels`)
                .setColor(0xFFA500)
                .addFields([
                  {
                    name: '👤 User',
                    value: `<@${interaction.user.id}> (${interaction.user.tag})`,
                    inline: true
                  },
                  {
                    name: '❓ Question',
                    value: question.question,
                    inline: false
                  },
                  {
                    name: '💬 Answer',
                    value: answer,
                    inline: false
                  },
                  {
                    name: '🔍 Status',
                    value: 'Not found in any server channels',
                    inline: false
                  }
                ]);

              await adminChannel.send({ embeds: [warningEmbed] });
            }
          } catch (error) {
            console.error('Error sending verification warning:', error);
          }
        }
      } else if (foundInChannel) {
        // Log successful verification
        console.log(`Successfully verified "${answer}" for user ${interaction.user.tag} in ${searchResults.length} channels`);
        
        // Send success notification to admin channel if configured
        if (config.adminChannelId && question.logVerification) {
          try {
            const adminChannel = await guild.channels.fetch(config.adminChannelId);
            if (adminChannel) {
              const successEmbed = new EmbedBuilder()
                .setTitle('✅ Verification Success')
                .setDescription(`User response verified in server channels`)
                .setColor(0x00FF00)
                .addFields([
                  {
                    name: '👤 User',
                    value: `<@${interaction.user.id}> (${interaction.user.tag})`,
                    inline: true
                  },
                  {
                    name: '❓ Question',
                    value: question.question,
                    inline: false
                  },
                  {
                    name: '💬 Answer',
                    value: answer,
                    inline: false
                  },
                  {
                    name: '🔍 Found In',
                    value: searchResults.map(r => `<#${r.channelId}> (${r.messageCount} matches)`).join('\n'),
                    inline: false
                  }
                ]);

              await adminChannel.send({ embeds: [successEmbed] });
            }
          } catch (error) {
            console.error('Error sending verification success:', error);
          }
        }
      }

      return { valid: true };
    } catch (error) {
      console.error('Error in channel verification:', error);
      
      // If verification fails due to error, allow submission but log the error
      if (question.verificationMode === 'required') {
        return {
          valid: false,
          error: `❌ **Verification Error**\nUnable to verify your response due to a technical error. Please try again or contact an administrator.`
        };
      }
      
      return { valid: true };
    }
  }

  static async logFailedVerification(interaction, failedVerifications, config) {
    try {
      if (config.logChannelId) {
        const logChannel = await interaction.guild.channels.fetch(config.logChannelId);
        if (logChannel) {
          const failureEmbed = new EmbedBuilder()
            .setTitle('❌ Application Verification Failed')
            .setDescription(`Application rejected due to verification failures`)
            .setColor(0xE74C3C)
            .addFields([
              {
                name: '👤 User',
                value: `<@${interaction.user.id}> (${interaction.user.tag})`,
                inline: true
              },
              {
                name: '⏰ Attempted',
                value: new Date().toLocaleString(),
                inline: true
              },
              {
                name: '🔍 Failed Verifications',
                value: failedVerifications.map(failure => 
                  `**${failure.question}**\nAnswer: "${failure.answer}"\nError: ${failure.error}`
                ).join('\n\n'),
                inline: false
              },
              {
                name: '🎭 Roles Assigned',
                value: 'None (verification failed)',
                inline: false
              }
            ]);

          await logChannel.send({ embeds: [failureEmbed] });
        }
      }
    } catch (error) {
      console.error('Error logging failed verification:', error);
    }
  }

  static async sendApplicationSummary(interaction, submission, roleAssignmentResult, config) {
    const embed = new EmbedBuilder()
      .setTitle('📋 Application Summary')
      .setDescription('Here\'s a summary of your application:')
      .setColor(roleAssignmentResult.verificationFailed ? 0xE74C3C : 0x00AE86)
      .addFields([
        {
          name: '📝 Your Responses',
          value: this.formatAnswers(submission.answers, config.questions),
          inline: false
        },
        {
          name: '📊 Application Status',
          value: roleAssignmentResult.verificationFailed ? '❌ Not Approved' : '✅ Approved',
          inline: true
        },
        {
          name: '⏰ Processed',
          value: new Date().toLocaleString(),
          inline: true
        }
      ]);

    if (!roleAssignmentResult.verificationFailed && roleAssignmentResult.assignedRoles.length > 0) {
      embed.addFields([
        {
          name: '🎭 Roles Assigned',
          value: roleAssignmentResult.assignedRoles.map(roleId => `<@&${roleId}>`).join(', '),
          inline: false
        }
      ]);
    }

    embed.setFooter({ 
      text: roleAssignmentResult.verificationFailed
        ? 'If you have questions about this decision, please contact our staff team.'
        : 'Welcome to the community! If you have any questions, feel free to ask our staff.'
    });

    await interaction.followUp({
      embeds: [embed],
      flags: MessageFlags.Ephemeral
    });
  }

  static async logSuccessfulVerification(interaction, verificationResults, assignedRoles, config) {
    try {
      // Only log if logApplicationVerification is enabled (default to false for cleaner logs)
      if (config.logChannelId && config.logApplicationVerification !== false) {
        const logChannel = await interaction.guild.channels.fetch(config.logChannelId);
        if (logChannel) {
          const successEmbed = new EmbedBuilder()
            .setTitle('✅ Application Verification Successful')
            .setDescription(`Application processed with successful verification`)
            .setColor(0x00FF00)
            .addFields([
              {
                name: '👤 User',
                value: `<@${interaction.user.id}> (${interaction.user.tag})`,
                inline: true
              },
              {
                name: '⏰ Processed',
                value: new Date().toLocaleString(),
                inline: true
              },
              {
                name: '🔍 Verification Results',
                value: verificationResults.map(result => 
                  `**${result.question}**: ${result.verified ? '✅ Verified' : '⚠️ Not verified'}\nAnswer: "${result.answer}"`
                ).join('\n\n'),
                inline: false
              },
              {
                name: '🎭 Roles Assigned',
                value: assignedRoles.length > 0 
                  ? assignedRoles.map(role => `<@&${role}>`).join(', ')
                  : 'None (no role assignments configured)',
                inline: false
              }
            ]);

          await logChannel.send({ embeds: [successEmbed] });
        }
      }
    } catch (error) {
      console.error('Error logging successful verification:', error);
    }
  }
  static async sendFormSubmissionEmbedWithRetry(interaction, submission, config, maxRetries = 3) {
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        await this.sendFormSubmissionEmbed(interaction, submission, config);
        console.log(`✅ Form submission embed sent successfully on attempt ${attempt}`);
        return; // Success, exit retry loop
      } catch (error) {
        console.error(`❌ Attempt ${attempt} failed to send form submission embed:`, error);
        
        if (attempt === maxRetries) {
          // Final attempt failed, send fail-safe response
          console.error('🚨 All attempts failed, sending fail-safe response');
          await this.sendFailSafeResponse(interaction, submission);
        } else {
          // Wait before retry
          await new Promise(resolve => setTimeout(resolve, 1000 * attempt));
        }
      }
    }
  }

  static async sendAccessUpdateEmbedWithRetry(interaction, submission, failedVerifications, maxRetries = 3) {
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        await this.sendAccessUpdateEmbed(interaction, submission, failedVerifications);
        console.log(`✅ Access update embed sent successfully on attempt ${attempt}`);
        return; // Success, exit retry loop
      } catch (error) {
        console.error(`❌ Attempt ${attempt} failed to send access update embed:`, error);
        
        if (attempt === maxRetries) {
          // Final attempt failed, send fail-safe response
          console.error('🚨 All attempts failed, sending fail-safe response');
          await this.sendFailSafeResponse(interaction, submission);
        } else {
          // Wait before retry
          await new Promise(resolve => setTimeout(resolve, 1000 * attempt));
        }
      }
    }
  }

  static async sendFailSafeResponse(interaction, submission) {
    console.log('🚨 FAIL-SAFE: Sending guaranteed response to user');
    
    const failSafeEmbed = new EmbedBuilder()
      .setTitle('📄 Form Submission')
      .setDescription('Your application has been received and is being processed. You will be contacted if additional information is needed.')
      .setColor(0x3498DB);

    try {
      await interaction.followUp({
        embeds: [failSafeEmbed],
        flags: MessageFlags.Ephemeral
      });
      console.log('✅ Fail-safe response sent via followUp');
    } catch (error) {
      console.error('🚨 CRITICAL: Even fail-safe response failed:', error);
      
      // Last resort: try basic text message
      try {
        const basicMessage = 'Your application has been received and is being processed.';
        
        await interaction.followUp({
          content: basicMessage,
          flags: MessageFlags.Ephemeral
        });
        console.log('✅ Last resort basic message sent');
      } catch (finalError) {
        console.error('🚨 ABSOLUTE FAILURE: Could not send any response to user:', finalError);
        // Log this critical failure for manual intervention
        this.logCriticalFailure(submission, finalError);
      }
    }
  }

  static logCriticalFailure(submission, error) {
    try {
      const failureLog = {
        timestamp: new Date().toISOString(),
        userId: submission.userId,
        username: submission.username,
        guildId: submission.guildId,
        error: error.message,
        stack: error.stack,
        submission: submission
      };
      
      const logFile = path.join(__dirname, '..', 'critical_failures.json');
      let failures = [];
      
      if (fs.existsSync(logFile)) {
        failures = JSON.parse(fs.readFileSync(logFile, 'utf8'));
      }
      
      failures.push(failureLog);
      fs.writeFileSync(logFile, JSON.stringify(failures, null, 2));
      
      console.error('🚨 Critical failure logged to critical_failures.json');
    } catch (logError) {
      console.error('🚨 Could not even log the critical failure:', logError);
    }
  }
  // Handle single-type application start (direct)
  static async handleSingleApplicationStart(interaction) {
    const config = this.loadConfig(interaction.guildId);
    
    if (!config || !config.enabled) {
      await interaction.reply({
        content: '❌ Application system is currently disabled.',
        flags: MessageFlags.Ephemeral
      });
      return;
    }

    if (!config.applicationTypes || Object.keys(config.applicationTypes).length === 0) {
      await interaction.reply({
        content: '❌ No application types are currently available.',
        flags: MessageFlags.Ephemeral
      });
      return;
    }

    const types = Object.keys(config.applicationTypes);
    const defaultType = config.defaultApplicationType || types[0];
    
    // Start the application directly with the default/first type
    await this.startApplicationForType(interaction, defaultType);
  }
}

module.exports = ApplicationHandler;