{"dm_approved_with_feedback": {"description": "DM flow - Application approved with answer feedback", "conversation": ["Bot: 📋 Server Application Form (embed) - Welcome to the application process!", "User: *clicks Start Form*", "Bot: Step 1: Your Full Name (embed) - Please provide your response below.", "User: <PERSON>", "Bot: ✓ Answer recorded. Next question coming up... (blue embed)", "(1.5 second delay)", "Bot: Step 2: <PERSON>ail Address (embed) - Please provide your response below.", "User: <EMAIL>", "Bot: ✓ Answer recorded. Next question coming up... (blue embed)", "(1.5 second delay)", "Bot: Step 3: Phone Number (embed) - Please provide your response below.", "User: 555-1234", "Bot: ✓ Answer recorded. Next question coming up... (blue embed)", "(1.5 second delay)", "Bot: 📝 Thank you for completing the application! Your responses have been received and are being reviewed. You'll get a response shortly. (green embed)", "(3 second processing delay)", "Bot: ✅ Great news! Your application has been approved. Welcome to our community! (green embed)", "Process complete - No summary sent to user"]}, "modal_rejected_with_feedback": {"description": "Modal flow - Application rejected with answer feedback", "conversation": ["Bot: (server) 📋 Server Application Form (embed) - DMs closed, using modal system", "User: *clicks Start Form*", "Bot: (modal) Question 1 of 3 - Text input modal", "User: *submits modal*", "Bot: (ephemeral) ✓ Answer recorded. Next question coming up... (blue embed)", "(1.5 second delay)", "Bot: (modal) Question 2 of 3 - Text input modal", "User: *submits modal*", "Bot: (ephemeral) ✓ Answer recorded. Next question coming up... (blue embed)", "(1.5 second delay)", "Bot: (modal) Question 3 of 3 - Text input modal", "User: *submits modal*", "Bot: (ephemeral) ✓ Answer recorded. Next question coming up... (blue embed)", "(1.5 second delay)", "Bot: (ephemeral) 📝 Thank you for completing the application! Your responses have been received and are being reviewed. You'll get a response shortly. (green embed)", "(3 second processing delay)", "Bot: (ephemeral) ❌ Thank you for your application. Unfortunately, we cannot approve your request at this time. Please feel free to contact our staff if you have any questions. (red embed)", "Process complete - No summary sent to user"]}, "choice_question_flow": {"description": "Choice questions with answer feedback", "conversation": ["<PERSON><PERSON>: What's your favorite programming language? (embed with dropdown)", "User: *selects JavaScript*", "Bot: ✓ Answer recorded. Next question coming up... (blue embed)", "(1.5 second delay)", "<PERSON><PERSON>: What's your experience level? (embed with dropdown)", "User: *selects Intermediate*", "Bot: ✓ Answer recorded. Next question coming up... (blue embed)", "(1.5 second delay)", "Bot: 📝 Thank you for completing the application! (green embed)", "(3 second processing delay)", "Bot: ✅ Great news! Your application has been approved. Welcome to our community! (green embed)"]}}