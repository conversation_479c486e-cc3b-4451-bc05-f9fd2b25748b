/**
 * Enhanced Role Matching Handler
 * Main orchestrator for the enhanced role matching system
 */

const { <PERSON><PERSON><PERSON><PERSON><PERSON>, ActionRowBuilder, ButtonBuilder, ButtonStyle } = require('discord.js');
const enhancedRoleMatchingEngine = require('./enhancedRoleMatchingEngine');
const enhancedAdminInterface = require('./enhancedAdminInterface');
const smartRoleAssignmentSystem = require('./smartRoleAssignmentSystem');
const roleMatchingConfig = require('./roleMatchingConfig');
const automaticRoleSelector = require('./automaticRoleSelector');

class EnhancedRoleMatchingHandler {
    constructor() {
        this.performanceMetrics = new Map();
        this.setupCleanupInterval();
    }
    
    /**
     * Fully automatic role matching and selection (no user interaction)
     * @param {string} intendedRoleName - The intended role name
     * @param {Collection} existingRoles - Collection of existing guild roles
     * @param {Object} context - Context information (guild, user, channel info, etc.)
     * @returns {Object} Automatic processing result
     */
    async processAutomaticRoleMatching(intendedRoleName, existingRoles, context) {
        const startTime = Date.now();

        try {
            console.log(`[AUTOMATIC_ROLE_MATCHING] Processing "${intendedRoleName}" automatically`);

            // Get configuration for this guild
            const config = roleMatchingConfig.getConfig(context.guildId);

            // Validate that automatic mode is enabled
            if (!config.fullyAutomatic) {
                console.warn(`[AUTOMATIC_ROLE_MATCHING] Automatic mode not enabled for guild ${context.guildId}`);
                return await this.processRoleMatching(intendedRoleName, existingRoles, context);
            }

            // Use automatic role selector to find best match
            const selection = await automaticRoleSelector.selectBestRole(
                intendedRoleName,
                existingRoles,
                context.guildId
            );

            console.log(`[AUTOMATIC_ROLE_MATCHING] Automatic selection: ${selection.action} - ${selection.reason}`);

            // Record performance metrics
            this.recordPerformance(context.guildId, Date.now() - startTime, selection.action);

            if (selection.action === 'use_existing') {
                // Automatically use the selected existing role
                return await this.handleAutomaticRoleUsage(selection, context);
            } else {
                // Automatically create new role
                return await this.handleAutomaticRoleCreation(intendedRoleName, context, config);
            }

        } catch (error) {
            console.error(`[AUTOMATIC_ROLE_MATCHING] Error processing "${intendedRoleName}":`, error);
            return {
                success: false,
                error: `Automatic role matching failed: ${error.message}`,
                requiresInteraction: false
            };
        }
    }

    /**
     * Handle automatic usage of existing role
     */
    async handleAutomaticRoleUsage(selection, context) {
        console.log(`[AUTOMATIC_ROLE_MATCHING] Using existing role: ${selection.selectedRole.name} (${selection.score}% match)`);

        const channelResult = await smartRoleAssignmentSystem.createPrivateChannelWithRole({
            guild: context.guild,
            channelName: context.channelName || context.intendedRoleName,
            role: selection.selectedRole,
            channelType: context.channelType || 'text',
            categoryChannel: context.categoryChannel,
            userId: context.userId,
            sessionId: `auto_${Date.now()}`
        });

        if (!channelResult.success) {
            return {
                success: false,
                error: channelResult.error,
                requiresInteraction: false
            };
        }

        return {
            success: true,
            action: 'used_existing_role',
            role: selection.selectedRole,
            channel: channelResult.channel,
            score: selection.score,
            priority: selection.priority,
            reason: selection.reason,
            requiresInteraction: false,
            automatic: true
        };
    }

    /**
     * Handle automatic creation of new role
     */
    async handleAutomaticRoleCreation(intendedRoleName, context, config) {
        console.log(`[AUTOMATIC_ROLE_MATCHING] Creating new role: ${intendedRoleName}`);

        const roleResult = await smartRoleAssignmentSystem.createOrUpdateRole({
            guild: context.guild,
            roleName: intendedRoleName,
            roleOptions: context.roleOptions || {},
            action: 'create',
            userId: context.userId,
            sessionId: `auto_${Date.now()}`
        });

        if (!roleResult.success) {
            return {
                success: false,
                error: roleResult.error,
                requiresInteraction: false
            };
        }

        const channelResult = await smartRoleAssignmentSystem.createPrivateChannelWithRole({
            guild: context.guild,
            channelName: context.channelName || intendedRoleName,
            role: roleResult.role,
            channelType: context.channelType || 'text',
            categoryChannel: context.categoryChannel,
            userId: context.userId,
            sessionId: `auto_${Date.now()}`
        });

        if (!channelResult.success) {
            // Clean up the role if channel creation failed
            await roleResult.role.delete().catch(console.error);
            return {
                success: false,
                error: channelResult.error,
                requiresInteraction: false
            };
        }

        return {
            success: true,
            action: 'created_new_role',
            role: roleResult.role,
            channel: channelResult.channel,
            reason: `No suitable existing roles found above ${config.minimumMatchThreshold}% threshold`,
            requiresInteraction: false,
            automatic: true
        };
    }

    /**
     * Legacy method for backward compatibility (now redirects to automatic)
     * @param {string} intendedRoleName - The intended role name
     * @param {Collection} existingRoles - Collection of existing guild roles
     * @param {Object} context - Context information (guild, user, channel info, etc.)
     * @returns {Object} Result object with matches and interface components
     */
    async processRoleMatching(intendedRoleName, existingRoles, context) {
        const startTime = Date.now();
        
        try {
            // Get configuration for this guild
            const config = roleMatchingConfig.getConfig(context.guildId);
            
            // Find role matches using the enhanced engine
            const matches = enhancedRoleMatchingEngine.findMatches(
                intendedRoleName,
                existingRoles,
                context.guildId,
                context.operationType || 'private_channel_creation'
            );
            
            // Record performance metrics
            this.recordPerformance(context.guildId, Date.now() - startTime, matches.length);
            
            // If no matches found and below threshold, proceed with creation
            if (matches.length === 0) {
                return await this.handleNoMatches(intendedRoleName, context, config);
            }
            
            // Check if this is individual channel creation mode (force admin confirmation)
            const isIndividualChannelCreation = context.sessionId && context.sessionId.includes('private_channel_');

            // If exact match found and auto-use is enabled (but not for individual channel creation)
            if (config.autoUseExactMatch && matches[0].matchType === 'exact' && !isIndividualChannelCreation) {
                return await this.handleAutoUseExactMatch(matches[0], context);
            }

            // Check for extra text elements that require admin confirmation (private channels only)
            if (context.operationType === 'private_channel_creation') {
                const extraTextMatches = matches.filter(match => match.requiresAdminConfirmation);
                if (extraTextMatches.length > 0) {
                    return await this.handleExtraTextConfirmation(intendedRoleName, extraTextMatches[0], context);
                }
            }

            // Create interactive interface for admin decision
            return await this.createInteractiveInterface(intendedRoleName, matches, context, config);
            
        } catch (error) {
            console.error('[ENHANCED_ROLE_MATCHING] Error in processRoleMatching:', error);
            return {
                success: false,
                error: error.message,
                requiresInteraction: false
            };
        }
    }
    
    /**
     * Handle case when no matches are found
     */
    async handleNoMatches(intendedRoleName, context, config) {
        console.log(`[ENHANCED_ROLE_MATCHING] No matches found for "${intendedRoleName}", proceeding with creation`);
        
        // Create role and channel directly
        const roleResult = await smartRoleAssignmentSystem.createOrUpdateRole({
            guild: context.guild,
            roleName: intendedRoleName,
            roleOptions: context.roleOptions || {},
            action: 'create',
            userId: context.userId,
            sessionId: context.sessionId
        });
        
        if (!roleResult.success) {
            return {
                success: false,
                error: roleResult.error,
                requiresInteraction: false
            };
        }
        
        const channelResult = await smartRoleAssignmentSystem.createPrivateChannelWithRole({
            guild: context.guild,
            channelName: context.channelName || intendedRoleName,
            role: roleResult.role,
            channelType: context.channelType || 'text',
            categoryChannel: context.categoryChannel,
            userId: context.userId,
            sessionId: context.sessionId
        });
        
        return {
            success: channelResult.success,
            error: channelResult.error,
            requiresInteraction: false,
            result: {
                action: 'created_new',
                role: roleResult.role,
                channel: channelResult.channel,
                auditIds: [roleResult.auditId, channelResult.auditId]
            }
        };
    }

    /**
     * Process multiple role matching operations automatically (no user interaction)
     * @param {Array} roleNames - Array of intended role names
     * @param {Collection} existingRoles - Collection of existing roles
     * @param {Object} context - Context object with guild, user info, etc.
     * @returns {Object} Automatic batch processing result
     */
    async processAutomaticBatchRoleMatching(roleNames, existingRoles, context) {
        const config = roleMatchingConfig.getConfig(context.guildId);

        console.log(`[AUTOMATIC_BATCH_PROCESSING] Processing ${roleNames.length} roles automatically`);

        const results = [];
        const summary = {
            total: roleNames.length,
            successful: 0,
            failed: 0,
            usedExisting: 0,
            createdNew: 0,
            errors: []
        };

        // Process each role name automatically
        for (const roleName of roleNames) {
            if (!roleName || !roleName.trim()) {
                summary.failed++;
                results.push({
                    name: roleName,
                    success: false,
                    error: 'Empty or invalid role name',
                    action: 'skipped'
                });
                continue;
            }

            const trimmedName = roleName.trim();
            console.log(`[AUTOMATIC_BATCH_PROCESSING] Processing "${trimmedName}"`);

            try {
                // Use automatic role matching for each role
                const result = await this.processAutomaticRoleMatching(
                    trimmedName,
                    existingRoles,
                    { ...context, intendedRoleName: trimmedName, channelName: trimmedName }
                );

                if (result.success) {
                    summary.successful++;
                    if (result.action === 'used_existing_role') {
                        summary.usedExisting++;
                    } else if (result.action === 'created_new_role') {
                        summary.createdNew++;
                    }

                    results.push({
                        name: trimmedName,
                        success: true,
                        action: result.action,
                        role: result.role,
                        channel: result.channel,
                        score: result.score,
                        priority: result.priority,
                        reason: result.reason
                    });

                    console.log(`[AUTOMATIC_BATCH_PROCESSING] ✅ "${trimmedName}" - ${result.action}`);
                } else {
                    summary.failed++;
                    summary.errors.push(`${trimmedName}: ${result.error}`);

                    results.push({
                        name: trimmedName,
                        success: false,
                        error: result.error,
                        action: 'failed'
                    });

                    console.log(`[AUTOMATIC_BATCH_PROCESSING] ❌ "${trimmedName}" - ${result.error}`);
                }
            } catch (error) {
                summary.failed++;
                summary.errors.push(`${trimmedName}: ${error.message}`);

                results.push({
                    name: trimmedName,
                    success: false,
                    error: error.message,
                    action: 'error'
                });

                console.error(`[AUTOMATIC_BATCH_PROCESSING] ❌ "${trimmedName}" - Error:`, error);
            }
        }

        console.log(`[AUTOMATIC_BATCH_PROCESSING] Completed: ${summary.successful}/${summary.total} successful`);

        return {
            success: summary.failed === 0 || summary.successful > 0,
            results: results,
            summary: summary,
            requiresInteraction: false,
            automatic: true,
            batchMode: true
        };
    }

    /**
     * Handle automatic use of exact match
     */
    async handleAutoUseExactMatch(match, context) {
        console.log(`[ENHANCED_ROLE_MATCHING] Auto-using exact match: ${match.role.name}`);
        
        const channelResult = await smartRoleAssignmentSystem.createPrivateChannelWithRole({
            guild: context.guild,
            channelName: context.channelName || match.role.name,
            role: match.role,
            channelType: context.channelType || 'text',
            categoryChannel: context.categoryChannel,
            userId: context.userId,
            sessionId: context.sessionId
        });
        
        return {
            success: channelResult.success,
            error: channelResult.error,
            requiresInteraction: false,
            result: {
                action: 'used_existing',
                role: match.role,
                channel: channelResult.channel,
                matchScore: match.score,
                auditIds: [channelResult.auditId]
            }
        };
    }
    
    /**
     * Create interactive interface for admin decision
     */
    async createInteractiveInterface(intendedRoleName, matches, context, config) {
        const sessionId = enhancedAdminInterface.generateSessionId(context.userId, context.guildId);
        
        // Store session data
        enhancedAdminInterface.storeSession(sessionId, {
            userId: context.userId,
            guildId: context.guildId,
            intendedRoleName,
            channelName: context.channelName || intendedRoleName,
            channelType: context.channelType || 'text',
            categoryChannel: context.categoryChannel,
            roleOptions: context.roleOptions || {},
            matches,
            operationType: context.operationType || 'private_channel_creation',
            config
        });
        
        // Create interface components
        const { embed, components } = enhancedAdminInterface.createRoleSelectionInterface(
            intendedRoleName,
            context.channelName || intendedRoleName,
            matches,
            sessionId,
            config
        );
        
        return {
            success: true,
            requiresInteraction: true,
            sessionId,
            embed,
            components,
            matches
        };
    }
    
    /**
     * Handle admin decision from interactive interface
     * @param {Object} interaction - Discord interaction object
     * @returns {Object} Result object
     */
    async handleAdminDecision(interaction) {
        const customId = interaction.customId;
        const parts = customId.split('_');

        // Parse interaction type and session ID
        if (!customId.startsWith('enhanced_')) {
            throw new Error('Invalid interaction ID format');
        }

        let actionType, action, sessionId;

        // Handle dropdown selection: enhanced_role_select_sessionId
        if (parts[2] === 'select') {
            actionType = 'role';
            action = 'select';
            sessionId = parts.slice(3).join('_');
        } else {
            // Handle button interactions: enhanced_role_action_sessionId or enhanced_confirm_action_sessionId
            actionType = parts[1]; // role, confirm
            action = parts[2]; // use, create, modify, replace, cancel, yes, no
            sessionId = parts.slice(3).join('_');
        }

        console.log(`[ENHANCED_ROLE_MATCHING] Handling decision: ${actionType}_${action} for session: ${sessionId}`);
        
        // Get session data
        const sessionData = enhancedAdminInterface.getSession(sessionId);
        if (!sessionData) {
            return {
                success: false,
                error: 'Session expired or not found. Please try the operation again.',
                ephemeral: true
            };
        }
        
        try {
            let result;
            
            switch (`${actionType}_${action}`) {
                case 'role_select':
                    result = await this.handleRoleSelection(interaction, sessionData);
                    break;
                case 'role_use':
                    result = await this.handleUseExistingRole(interaction, sessionData);
                    break;
                case 'role_create':
                    result = await this.handleCreateNewRole(interaction, sessionData);
                    break;
                case 'role_modify':
                    result = await this.handleCreateWithModifiedName(interaction, sessionData);
                    break;
                case 'role_replace':
                    result = await this.handleReplaceExistingRole(interaction, sessionData);
                    break;
                case 'role_cancel':
                    result = await this.handleCancelOperation(interaction, sessionData);
                    break;
                case 'confirm_yes':
                case 'confirm_no':
                    result = await this.handleConfirmationResponse(interaction, sessionData, action === 'yes');
                    break;
                default:
                    throw new Error(`Unknown action: ${actionType}_${action}`);
            }
            
            // Clean up session if operation is complete
            if (result.complete) {
                enhancedAdminInterface.deleteSession(sessionId);
            }
            
            return result;
            
        } catch (error) {
            console.error('[ENHANCED_ROLE_MATCHING] Error handling admin decision:', error);
            enhancedAdminInterface.deleteSession(sessionId);
            
            return {
                success: false,
                error: error.message,
                ephemeral: true,
                complete: true
            };
        }
    }

    /**
     * Handle role selection from dropdown
     */
    async handleRoleSelection(interaction, sessionData) {
        // This is just a selection from dropdown, not a final action
        // We need to update the interface to show the selected role and ask for confirmation

        if (!interaction.isStringSelectMenu()) {
            return {
                success: false,
                error: 'Invalid interaction type for role selection.',
                ephemeral: true,
                complete: true
            };
        }

        const selectedIndex = parseInt(interaction.values[0]);
        if (isNaN(selectedIndex) || !sessionData.matches[selectedIndex]) {
            return {
                success: false,
                error: 'Invalid role selection.',
                ephemeral: true,
                complete: true
            };
        }

        const selectedRole = sessionData.matches[selectedIndex];

        // Update the session data to remember the selection
        sessionData.selectedRoleIndex = selectedIndex;
        enhancedAdminInterface.storeSession(
            interaction.customId.split('_').slice(3).join('_'),
            sessionData
        );

        // Create updated interface showing the selected role
        const embed = new EmbedBuilder()
            .setTitle('✅ Role Selected')
            .setColor(0x00FF00)
            .setDescription(
                `**Selected Role:** ${selectedRole.role}\n` +
                `**Match Score:** ${Math.round(selectedRole.score)}%\n` +
                `**Match Type:** ${selectedRole.details}\n` +
                `**Members:** ${selectedRole.role.members.size}\n` +
                `**Created:** <t:${Math.floor(selectedRole.role.createdTimestamp / 1000)}:R>\n\n` +
                `**What would you like to do with this role?**`
            )
            .setTimestamp();

        // Create action buttons for the selected role
        const components = [
            new ActionRowBuilder()
                .addComponents(
                    new ButtonBuilder()
                        .setCustomId(`enhanced_role_use_${interaction.customId.split('_').slice(3).join('_')}`)
                        .setLabel('Use This Role')
                        .setStyle(ButtonStyle.Success)
                        .setEmoji('✅'),
                    new ButtonBuilder()
                        .setCustomId(`enhanced_role_create_${interaction.customId.split('_').slice(3).join('_')}`)
                        .setLabel('Create New Role Instead')
                        .setStyle(ButtonStyle.Primary)
                        .setEmoji('➕'),
                    new ButtonBuilder()
                        .setCustomId(`enhanced_role_replace_${interaction.customId.split('_').slice(3).join('_')}`)
                        .setLabel('Replace This Role')
                        .setStyle(ButtonStyle.Danger)
                        .setEmoji('🔄')
                ),
            new ActionRowBuilder()
                .addComponents(
                    new ButtonBuilder()
                        .setCustomId(`enhanced_role_cancel_${interaction.customId.split('_').slice(3).join('_')}`)
                        .setLabel('Cancel Operation')
                        .setStyle(ButtonStyle.Secondary)
                        .setEmoji('❌')
                )
        ];

        return {
            success: true,
            embed,
            components,
            ephemeral: true,
            complete: false
        };
    }

    /**
     * Handle using existing role
     */
    async handleUseExistingRole(interaction, sessionData) {
        // Determine which role to use (from previous selection or first match)
        let selectedRole = sessionData.matches[0].role;
        let matchScore = sessionData.matches[0].score;

        // Check if user previously selected a specific role from dropdown
        if (sessionData.selectedRoleIndex !== undefined && sessionData.matches[sessionData.selectedRoleIndex]) {
            selectedRole = sessionData.matches[sessionData.selectedRoleIndex].role;
            matchScore = sessionData.matches[sessionData.selectedRoleIndex].score;
        }
        // Check if this is a direct dropdown selection
        else if (interaction.isStringSelectMenu && interaction.isStringSelectMenu()) {
            const selectedIndex = parseInt(interaction.values[0]);
            if (!isNaN(selectedIndex) && sessionData.matches[selectedIndex]) {
                selectedRole = sessionData.matches[selectedIndex].role;
                matchScore = sessionData.matches[selectedIndex].score;
            }
        }
        
        const channelResult = await smartRoleAssignmentSystem.createPrivateChannelWithRole({
            guild: interaction.guild,
            channelName: sessionData.channelName,
            role: selectedRole,
            channelType: sessionData.channelType,
            categoryChannel: sessionData.categoryChannel,
            userId: sessionData.userId,
            sessionId: interaction.customId
        });
        
        if (!channelResult.success) {
            return {
                success: false,
                error: channelResult.error,
                ephemeral: true,
                complete: true
            };
        }
        
        const resultEmbed = enhancedAdminInterface.createResultEmbed('use', {
            channel: channelResult.channel,
            role: selectedRole,
            matchScore
        }, sessionData);
        
        return {
            success: true,
            embed: resultEmbed,
            ephemeral: true,
            complete: true
        };
    }
    
    /**
     * Handle creating new role
     */
    async handleCreateNewRole(interaction, sessionData) {
        const roleResult = await smartRoleAssignmentSystem.createOrUpdateRole({
            guild: interaction.guild,
            roleName: sessionData.intendedRoleName,
            roleOptions: sessionData.roleOptions,
            action: 'create',
            userId: sessionData.userId,
            sessionId: interaction.customId
        });
        
        if (!roleResult.success) {
            return {
                success: false,
                error: roleResult.error,
                ephemeral: true,
                complete: true
            };
        }
        
        const channelResult = await smartRoleAssignmentSystem.createPrivateChannelWithRole({
            guild: interaction.guild,
            channelName: sessionData.channelName,
            role: roleResult.role,
            channelType: sessionData.channelType,
            categoryChannel: sessionData.categoryChannel,
            userId: sessionData.userId,
            sessionId: interaction.customId
        });
        
        if (!channelResult.success) {
            // Clean up the role if channel creation failed
            await roleResult.role.delete().catch(console.error);
            return {
                success: false,
                error: channelResult.error,
                ephemeral: true,
                complete: true
            };
        }
        
        const resultEmbed = enhancedAdminInterface.createResultEmbed('create', {
            channel: channelResult.channel,
            role: roleResult.role
        }, sessionData);
        
        return {
            success: true,
            embed: resultEmbed,
            ephemeral: true,
            complete: true
        };
    }
    
    /**
     * Handle creating role with modified name (show modal)
     */
    async handleCreateWithModifiedName(interaction, sessionData) {
        const modal = enhancedAdminInterface.createCustomNameModal(
            interaction.customId.split('_').slice(4).join('_'),
            sessionData.intendedRoleName
        );
        
        return {
            success: true,
            modal,
            ephemeral: true,
            complete: false
        };
    }
    
    /**
     * Handle replacing existing role (show confirmation)
     */
    async handleReplaceExistingRole(interaction, sessionData) {
        // Determine which role to replace
        let roleToReplace = sessionData.matches[0].role;
        
        if (interaction.isStringSelectMenu && interaction.isStringSelectMenu()) {
            const selectedIndex = parseInt(interaction.values[0]);
            if (!isNaN(selectedIndex) && sessionData.matches[selectedIndex]) {
                roleToReplace = sessionData.matches[selectedIndex].role;
            }
        }
        
        const { embed, components, confirmationId } = enhancedAdminInterface.createConfirmationDialog(
            'replace',
            sessionData,
            roleToReplace
        );
        
        // Store confirmation data
        enhancedAdminInterface.storeConfirmation(confirmationId, {
            sessionId: interaction.customId.split('_').slice(4).join('_'),
            action: 'replace',
            roleToReplace,
            sessionData
        });
        
        return {
            success: true,
            embed,
            components,
            ephemeral: true,
            complete: false
        };
    }
    
    /**
     * Handle cancel operation
     */
    async handleCancelOperation(interaction, sessionData) {
        const resultContent = `❌ **Operation cancelled.**\n\nNo changes were made for channel: \`${sessionData.channelName}\``;
        
        return {
            success: true,
            content: resultContent,
            ephemeral: true,
            complete: true
        };
    }
    
    /**
     * Handle confirmation response
     */
    async handleConfirmationResponse(interaction, sessionData, confirmed) {
        if (!confirmed) {
            return {
                success: true,
                content: '↩️ **Returned to main menu.**\n\nPlease make your selection again.',
                ephemeral: true,
                complete: false
            };
        }
        
        // Handle confirmed action based on stored confirmation data
        const confirmationId = interaction.customId.split('_').slice(3).join('_');
        const confirmationData = enhancedAdminInterface.getConfirmation(confirmationId);
        
        if (!confirmationData) {
            return {
                success: false,
                error: 'Confirmation data not found.',
                ephemeral: true,
                complete: true
            };
        }
        
        // Clean up confirmation data
        enhancedAdminInterface.deleteConfirmation(confirmationId);
        
        if (confirmationData.action === 'replace') {
            return await this.executeRoleReplacement(interaction, confirmationData);
        }
        
        return {
            success: false,
            error: 'Unknown confirmation action.',
            ephemeral: true,
            complete: true
        };
    }
    
    /**
     * Execute role replacement
     */
    async executeRoleReplacement(interaction, confirmationData) {
        const { roleToReplace, sessionData } = confirmationData;
        
        const roleResult = await smartRoleAssignmentSystem.createOrUpdateRole({
            guild: interaction.guild,
            roleName: sessionData.intendedRoleName,
            roleOptions: sessionData.roleOptions,
            existingRole: roleToReplace,
            action: 'replace',
            userId: sessionData.userId,
            sessionId: interaction.customId
        });
        
        if (!roleResult.success) {
            return {
                success: false,
                error: roleResult.error,
                ephemeral: true,
                complete: true
            };
        }
        
        const channelResult = await smartRoleAssignmentSystem.createPrivateChannelWithRole({
            guild: interaction.guild,
            channelName: sessionData.channelName,
            role: roleResult.role,
            channelType: sessionData.channelType,
            categoryChannel: sessionData.categoryChannel,
            userId: sessionData.userId,
            sessionId: interaction.customId
        });
        
        if (!channelResult.success) {
            return {
                success: false,
                error: channelResult.error,
                ephemeral: true,
                complete: true
            };
        }
        
        const resultEmbed = enhancedAdminInterface.createResultEmbed('replace', {
            channel: channelResult.channel,
            newRole: roleResult.role,
            oldRole: roleToReplace,
            membersTransferred: roleToReplace.members.size
        }, sessionData);
        
        return {
            success: true,
            embed: resultEmbed,
            ephemeral: true,
            complete: true
        };
    }
    
    /**
     * Record performance metrics
     */
    recordPerformance(guildId, processingTime, matchCount) {
        const key = `perf_${guildId}`;
        const existing = this.performanceMetrics.get(key) || { times: [], matches: [], count: 0 };
        
        existing.times.push(processingTime);
        existing.matches.push(matchCount);
        existing.count++;
        
        // Keep only last 100 measurements
        if (existing.times.length > 100) {
            existing.times.shift();
            existing.matches.shift();
        }
        
        this.performanceMetrics.set(key, existing);
    }
    
    /**
     * Get performance statistics
     */
    getPerformanceStats(guildId) {
        const key = `perf_${guildId}`;
        const data = this.performanceMetrics.get(key);
        
        if (!data || data.times.length === 0) {
            return null;
        }
        
        const avgTime = data.times.reduce((a, b) => a + b, 0) / data.times.length;
        const avgMatches = data.matches.reduce((a, b) => a + b, 0) / data.matches.length;
        const maxTime = Math.max(...data.times);
        const minTime = Math.min(...data.times);
        
        return {
            averageProcessingTime: Math.round(avgTime),
            averageMatches: Math.round(avgMatches * 10) / 10,
            maxProcessingTime: maxTime,
            minProcessingTime: minTime,
            totalOperations: data.count,
            recentOperations: data.times.length
        };
    }
    
    /**
     * Setup cleanup interval for sessions and performance data
     */
    setupCleanupInterval() {
        setInterval(() => {
            try {
                // Clean up admin interface sessions
                enhancedAdminInterface.cleanup();

                // Clean up assignment system audit logs
                smartRoleAssignmentSystem.cleanupAuditLog();

                // Clean up performance metrics (keep only recent data)
                for (const [key, data] of this.performanceMetrics.entries()) {
                    if (data.times.length > 100) {
                        data.times = data.times.slice(-50);
                        data.matches = data.matches.slice(-50);
                    }
                }

                console.log('[ENHANCED_ROLE_MATCHING] Cleanup completed');
            } catch (error) {
                console.error('[ENHANCED_ROLE_MATCHING] Error during cleanup:', error);
            }
        }, 5 * 60 * 1000); // Every 5 minutes
    }

    /**
     * Handle errors gracefully with user-friendly messages
     */
    handleError(error, context = {}) {
        const errorId = `ERR_${Date.now()}_${Math.random().toString(36).substr(2, 6)}`;

        console.error(`[ENHANCED_ROLE_MATCHING] Error ${errorId}:`, error);
        console.error('[ENHANCED_ROLE_MATCHING] Context:', context);

        // Log error for debugging
        smartRoleAssignmentSystem.logAssignment({
            type: 'error',
            errorId,
            error: error.message,
            stack: error.stack,
            context,
            timestamp: Date.now()
        });

        // Return user-friendly error message
        let userMessage = 'An unexpected error occurred. ';

        if (error.message.includes('Missing Permissions')) {
            userMessage = 'I don\'t have the required permissions to perform this action. Please check my role permissions.';
        } else if (error.message.includes('Unknown Role') || error.message.includes('Unknown Channel')) {
            userMessage = 'The role or channel no longer exists. It may have been deleted.';
        } else if (error.message.includes('Rate limit')) {
            userMessage = 'Discord rate limit reached. Please try again in a few moments.';
        } else if (error.message.includes('timeout') || error.message.includes('expired')) {
            userMessage = 'The operation timed out. Please try again.';
        } else {
            userMessage += `Error ID: ${errorId}`;
        }

        return {
            success: false,
            error: userMessage,
            errorId,
            ephemeral: true,
            complete: true
        };
    }

    /**
     * Validate system health and requirements
     */
    async validateSystemHealth(guild) {
        const issues = [];

        try {
            // Check bot permissions
            const botMember = guild.members.me;
            if (!botMember) {
                issues.push('Bot member not found in guild');
                return { healthy: false, issues };
            }

            const requiredPermissions = [
                'ManageRoles',
                'ManageChannels',
                'ViewChannel',
                'SendMessages'
            ];

            const missingPermissions = requiredPermissions.filter(perm =>
                !botMember.permissions.has(perm)
            );

            if (missingPermissions.length > 0) {
                issues.push(`Missing permissions: ${missingPermissions.join(', ')}`);
            }

            // Check role hierarchy
            const botHighestRole = botMember.roles.highest;
            const manageableRoles = guild.roles.cache.filter(role =>
                role.comparePositionTo(botHighestRole) < 0 && !role.managed
            );

            if (manageableRoles.size === 0) {
                issues.push('Bot cannot manage any roles due to hierarchy restrictions');
            }

            // Check system components
            try {
                roleMatchingConfig.getConfig(guild.id);
            } catch (error) {
                issues.push(`Configuration system error: ${error.message}`);
            }

            return {
                healthy: issues.length === 0,
                issues,
                manageableRoles: manageableRoles.size,
                totalRoles: guild.roles.cache.size
            };

        } catch (error) {
            issues.push(`Health check failed: ${error.message}`);
            return { healthy: false, issues };
        }
    }
}

module.exports = new EnhancedRoleMatchingHandler();
