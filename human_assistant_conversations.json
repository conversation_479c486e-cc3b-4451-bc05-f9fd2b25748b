{"dm_approved_flow": {"description": "DM flow - Application approved", "conversation": ["Bot: 📋 Server Application Form - Welcome to the application process!", "User: *clicks Start Form*", "Bot: Step 1: Your Full Name - Please provide your response below.", "User: <PERSON>", "Bot: Step 2: <PERSON>ail Address - Please provide your response below.", "User: <EMAIL>", "Bot: Step 3: Phone Number - Please provide your response below.", "User: 555-1234", "Bot: 📝 Thank you for completing the application! Your responses have been received and are being reviewed. You'll get a response shortly.", "Bo<PERSON>: (3-second pause)", "Bot: ✅ Great news! Your application has been approved. Welcome to our community!", "Bot: 📋 Application Summary (embed with all responses, Approved status, roles assigned, welcome message)"]}, "modal_rejected_flow": {"description": "Modal flow - Application rejected", "conversation": ["Bot: (server) 📋 Server Application Form - DMs closed, using modal system", "User: *clicks Start Form*", "Bot: (modal) Question 1 of 3 - Text input", "User: *submits modal*", "Bot: (modal) Question 2 of 3 - Text input", "User: *submits modal*", "Bot: (modal) Question 3 of 3 - Text input", "User: *submits modal*", "Bot: (ephemeral) 📝 Thank you for completing the application! Your responses have been received and are being reviewed. You'll get a response shortly.", "Bo<PERSON>: (3-second pause)", "Bot: (ephemeral) ❌ Thank you for your application. Unfortunately, we cannot approve your request at this time. Please feel free to contact our staff if you have any questions.", "Bot: (ephemeral) 📋 Application Summary (embed with all responses, Not Approved status, contact staff message)"]}}