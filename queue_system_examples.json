{"single_user_flow": {"description": "Single user application with queue processing", "flow": ["User: Completes 3 questions", "Bot: ✓ Answer recorded. Next question coming up... (Q1)", "Bot: ✓ Answer recorded. Next question coming up... (Q2)", "Bot: ✅ Your application has been successfully submitted. Please wait while we review your responses. (Q3 - Final)", "System: Application added to queue", "Admin Channel: 📥 New application received from User123. Added to review queue.", "System: Background processing (3 seconds)", "Bot: ✅ Application Approved - Your application has been approved. For any help or information, please contact a staff member or admin.", "System: Queue status updated to 'approved'"]}, "multiple_users_flow": {"description": "Multiple users applying simultaneously (scalability test)", "flow": ["User1, User2, User3: All complete applications simultaneously", "System: All applications added to queue in order", "Admin Channel: 📥 New application received from User1. Added to review queue.", "Admin Channel: 📥 New application received from User2. Added to review queue.", "Admin Channel: 📥 New application received from User3. Added to review queue.", "System: Applications processed one by one in background", "Bot: Sends individual decision messages to each user", "System: Queue statuses updated individually", "Result: No spam, no rate limits, smooth processing"]}, "queue_file_structure": {"description": "application_queue.json structure", "example": {"entry_1": {"guildId": "123456789", "userId": "987654321", "username": "User123#1234", "answers": {"0": "<PERSON>", "1": "<EMAIL>", "2": "Developer"}, "submittedAt": "2024-01-15T10:30:00.000Z", "status": "approved", "processedAt": "2024-01-15T10:30:03.000Z"}}}}