{"version": 3, "file": "responses.d.ts", "sourceRoot": "", "sources": ["responses.ts"], "names": [], "mappings": "AAAA,OAAO,KAAK,EAAE,mCAAmC,EAAE,MAAM,aAAa,CAAC;AACvE,OAAO,KAAK,EAAE,qBAAqB,EAAE,0BAA0B,EAAE,MAAM,YAAY,CAAC;AACpF,OAAO,KAAK,EAAE,iCAAiC,EAAE,MAAM,uBAAuB,CAAC;AAE/E;;GAEG;AACH,oBAAY,eAAe;IAC1B,IAAI,IAAI;IACR,kBAAkB,IAAA;IAClB,gBAAgB,IAAA;IAChB,8BAA8B,IAAA;IAC9B,WAAW,IAAA;CACX;AAED;;GAEG;AACH,MAAM,MAAM,sBAAsB,GAC/B,yCAAyC,GACzC,8CAA8C,GAC9C,sDAAsD,GACtD,2CAA2C,GAC3C,oCAAoC,GACpC,0BAA0B,GAC1B,mCAAmC,GACnC,2BAA2B,GAC3B,qCAAqC,CAAC;AAEzC,MAAM,WAAW,0BAA0B;IAC1C,IAAI,EAAE,uBAAuB,CAAC,IAAI,CAAC;CACnC;AAED,MAAM,WAAW,yCAAyC;IACzD,IAAI,EAAE,uBAAuB,CAAC,oCAAoC,CAAC;IACnE,IAAI,EAAE,qDAAqD,CAAC;CAC5D;AAED,MAAM,WAAW,2BAA2B;IAC3C,IAAI,EAAE,uBAAuB,CAAC,KAAK,CAAC;IACpC,IAAI,EAAE,uCAAuC,CAAC;CAC9C;AAED,MAAM,WAAW,qCAAqC;IACrD,IAAI,EAAE,uBAAuB,CAAC,eAAe,CAAC;CAC9C;AAED,MAAM,WAAW,8CAA8C;IAC9D,IAAI,EAAE,uBAAuB,CAAC,wBAAwB,CAAC;IACvD,IAAI,EAAE,kCAAkC,CAAC;CACzC;AAED,MAAM,WAAW,sDAAsD;IACtE,IAAI,EAAE,uBAAuB,CAAC,gCAAgC,CAAC;IAC/D,IAAI,CAAC,EAAE,IAAI,CAAC,kCAAkC,EAAE,OAAO,CAAC,CAAC;CACzD;AAED,MAAM,WAAW,2CAA2C;IAC3D,IAAI,EAAE,uBAAuB,CAAC,qBAAqB,CAAC;CACpD;AAED,MAAM,WAAW,mCAAmC;IACnD,IAAI,EAAE,uBAAuB,CAAC,aAAa,CAAC;IAC5C,IAAI,CAAC,EAAE,kCAAkC,CAAC;CAC1C;AAED,MAAM,WAAW,oCAAoC;IACpD,IAAI,EAAE,uBAAuB,CAAC,cAAc,CAAC;CAC7C;AAED;;GAEG;AACH,oBAAY,uBAAuB;IAClC;;OAEG;IACH,IAAI,IAAI;IACR;;OAEG;IACH,wBAAwB,IAAI;IAC5B;;OAEG;IACH,gCAAgC,IAAA;IAChC;;OAEG;IACH,qBAAqB,IAAA;IACrB;;OAEG;IACH,aAAa,IAAA;IACb;;OAEG;IACH,oCAAoC,IAAA;IACpC;;OAEG;IACH,KAAK,IAAA;IACL;;;;;OAKG;IACH,eAAe,KAAA;IAEf;;;;;OAKG;IACH,cAAc,KAAK;CACnB;AAED;;GAEG;AACH,MAAM,MAAM,kCAAkC,GAAG,IAAI,CAAC,mCAAmC,EAAE,YAAY,GAAG,UAAU,CAAC,CAAC;AAEtH,MAAM,WAAW,qDAAqD;IACrE,OAAO,CAAC,EAAE,iCAAiC,EAAE,CAAC;CAC9C;AAED;;GAEG;AACH,MAAM,WAAW,uCAAuC;IACvD;;OAEG;IACH,SAAS,EAAE,MAAM,CAAC;IAClB;;OAEG;IACH,KAAK,EAAE,MAAM,CAAC;IACd;;OAEG;IACH,UAAU,EAAE,qBAAqB,CAAC,0BAA0B,CAAC,EAAE,CAAC;CAChE"}