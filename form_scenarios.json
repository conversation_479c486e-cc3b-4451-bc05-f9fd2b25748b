{"scenario_1_dm_success": {"description": "User has DMs open - Complete form via DM", "flow": ["User clicks Apply Now", "<PERSON><PERSON> sends DM with welcome message", "User clicks Start Form in DM", "Questions presented one by one in DM", "Real-time verification feedback in DM", "Success message sent to DM", "Role assigned, logged to admin channel"], "outcome": "✅ Complete DM-based application process"}, "scenario_2_dm_closed": {"description": "User has DMs closed - Fallback to modal system", "flow": ["User clicks Apply Now", "Bot detects closed DMs", "Fallback message shown in server", "User clicks Start Form", "Questions presented via modals", "Verification feedback via ephemeral messages", "Success message in server", "Role assigned, logged to admin channel"], "outcome": "✅ Graceful fallback to modal system"}, "scenario_3_verification_failure": {"description": "Required verification fails - Retry system", "flow": ["User submits answer for required verification question", "Bot searches channels - not found", "❌ Verification failed message with retry button", "User clicks retry button", "Question presented again", "User submits corrected answer", "✅ Verification successful, continue"], "outcome": "✅ Retry system handles verification failures"}, "scenario_4_admin_revoke": {"description": "Admin revokes user access", "flow": ["User completes application successfully", "Professional log embed sent to admin channel", "Admin clicks Revoke Access button", "Modal opens for revocation reason", "Admin submits reason", "User roles removed", "Log embed updated with revocation details", "User notified via DM (if possible)"], "outcome": "✅ Complete revocation system with audit trail"}}