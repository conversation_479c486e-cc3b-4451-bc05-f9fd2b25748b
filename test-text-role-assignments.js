/**
 * Test script for text-based role assignments
 * This tests the new functionality that allows role assignments for text questions
 */

const fs = require('fs');
const path = require('path');

// Mock Discord.js objects for testing
const mockGuild = {
  id: '656611572021592064',
  roles: {
    fetch: async (roleId) => {
      const mockRoles = {
        '123456789012345678': { id: '123456789012345678', name: 'Moderator' },
        '123456789012345679': { id: '123456789012345679', name: 'Member' },
        '123456789012345680': { id: '123456789012345680', name: 'Admin' }
      };
      return mockRoles[roleId] || null;
    }
  },
  members: {
    fetch: async (userId) => ({
      id: userId,
      roles: {
        add: async (role, reason) => {
          console.log(`✅ Would assign role ${role.name} (${role.id}) to user ${userId}. Reason: ${reason}`);
          return true;
        },
        cache: new Map()
      }
    })
  }
};

const mockInteraction = {
  guild: mockGuild,
  user: { tag: 'TestUser#1234', id: '987654321098765432' }
};

// Test configuration with both choice and text questions
const testConfig = {
  applicationTypes: {
    'test_app': {
      name: 'Test Application',
      questions: [
        {
          question: 'What is your preferred role?',
          type: 'choice',
          options: ['Moderator', 'Helper', 'Member']
        },
        {
          question: 'What is your experience level?',
          type: 'text'
        },
        {
          question: 'What timezone are you in?',
          type: 'text'
        }
      ],
      roleAssignments: {
        // Choice question role assignments
        '0': {
          'Moderator': '123456789012345678',
          'Helper': '123456789012345679',
          'Member': '123456789012345680'
        },
        // Text question role assignments (case-insensitive)
        '1': {
          'beginner': '123456789012345680',
          'intermediate': '123456789012345679',
          'advanced': '123456789012345678',
          'expert': '123456789012345678'
        },
        '2': {
          'est': '123456789012345679',
          'pst': '123456789012345679',
          'cst': '123456789012345679',
          'mst': '123456789012345679',
          'utc': '123456789012345680'
        }
      }
    }
  }
};

// Test the role assignment logic
async function testRoleAssignment(answers, expectedRoles) {
  console.log('\n🧪 Testing role assignment...');
  console.log('Answers:', answers);
  console.log('Expected roles:', expectedRoles);
  
  const assignedRoles = [];
  
  // Simulate the role assignment logic from applicationHandler.js
  for (const [questionIndex, answer] of Object.entries(answers)) {
    const roleAssignments = testConfig.applicationTypes.test_app.roleAssignments[questionIndex];
    if (roleAssignments) {
      let roleId = null;
      
      // First try exact match (for choice questions and exact text matches)
      if (roleAssignments[answer]) {
        roleId = roleAssignments[answer];
      } else {
        // For text questions, try case-insensitive matching
        const normalizedAnswer = answer.toLowerCase().trim();
        roleId = roleAssignments[normalizedAnswer];
      }
      
      if (roleId) {
        try {
          const role = await mockGuild.roles.fetch(roleId);
          if (role) {
            const member = await mockGuild.members.fetch(mockInteraction.user.id);
            await member.roles.add(role, 'Automatic role assignment from verified application');
            assignedRoles.push(roleId);
            console.log(`✅ Assigned role ${role.name} for answer: "${answer}"`);
          }
        } catch (error) {
          console.error(`❌ Error assigning role ${roleId}:`, error);
        }
      } else {
        console.log(`ℹ️ No role mapping found for answer: "${answer}"`);
      }
    }
  }
  
  // Verify results
  const success = expectedRoles.every(roleId => assignedRoles.includes(roleId)) &&
                  assignedRoles.length === expectedRoles.length;
  
  console.log(`${success ? '✅' : '❌'} Test ${success ? 'PASSED' : 'FAILED'}`);
  console.log('Assigned roles:', assignedRoles);
  
  return success;
}

// Run tests
async function runTests() {
  console.log('🚀 Starting text-based role assignment tests...\n');
  
  let passedTests = 0;
  let totalTests = 0;
  
  // Test 1: Choice question role assignment
  totalTests++;
  console.log('📋 Test 1: Choice question role assignment');
  if (await testRoleAssignment(
    { '0': 'Moderator' },
    ['123456789012345678']
  )) {
    passedTests++;
  }
  
  // Test 2: Text question role assignment (exact case)
  totalTests++;
  console.log('\n📝 Test 2: Text question role assignment (exact case)');
  if (await testRoleAssignment(
    { '1': 'beginner' },
    ['123456789012345680']
  )) {
    passedTests++;
  }
  
  // Test 3: Text question role assignment (case-insensitive)
  totalTests++;
  console.log('\n📝 Test 3: Text question role assignment (case-insensitive)');
  if (await testRoleAssignment(
    { '1': 'ADVANCED' },
    ['123456789012345678']
  )) {
    passedTests++;
  }
  
  // Test 4: Text question role assignment (with spaces)
  totalTests++;
  console.log('\n📝 Test 4: Text question role assignment (with spaces)');
  if (await testRoleAssignment(
    { '1': '  Expert  ' },
    ['123456789012345678']
  )) {
    passedTests++;
  }
  
  // Test 5: Multiple role assignments
  totalTests++;
  console.log('\n🔄 Test 5: Multiple role assignments');
  if (await testRoleAssignment(
    { '0': 'Helper', '1': 'intermediate', '2': 'EST' },
    ['123456789012345679', '123456789012345679', '123456789012345679']
  )) {
    passedTests++;
  }
  
  // Test 6: No matching role
  totalTests++;
  console.log('\n❓ Test 6: No matching role');
  if (await testRoleAssignment(
    { '1': 'nonexistent' },
    []
  )) {
    passedTests++;
  }
  
  // Summary
  console.log('\n📊 Test Summary:');
  console.log(`✅ Passed: ${passedTests}/${totalTests}`);
  console.log(`❌ Failed: ${totalTests - passedTests}/${totalTests}`);
  
  if (passedTests === totalTests) {
    console.log('\n🎉 All tests passed! Text-based role assignments are working correctly.');
  } else {
    console.log('\n⚠️ Some tests failed. Please review the implementation.');
  }
  
  return passedTests === totalTests;
}

// Run the tests
if (require.main === module) {
  runTests().catch(console.error);
}

module.exports = { runTests, testRoleAssignment };
