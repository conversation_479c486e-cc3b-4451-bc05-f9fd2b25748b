{"successful_application": {"description": "User application approved - Clean form submission embed", "flow": ["User: Completes all application questions", "Bot: ✅ Your application has been successfully submitted. Please wait while we review your responses.", "System: Background processing (silent)", "Admin: 📥 New application received from User123. Added to review queue.", "System: Verification and role assignment (silent)", "Bot: 📄 Form Submission (embed with all user responses organized clearly)", "Process complete - single clean embed only"], "embed_content": {"title": "📄 Form Submission", "color": "Green", "fields": ["What is your name? → <PERSON>", "What is your email? → <EMAIL>", "What is your experience level? → Intermediate"]}}, "denied_application": {"description": "User application denied - Clean access update embed", "flow": ["User: Completes all application questions", "Bot: ✅ Your application has been successfully submitted. Please wait while we review your responses.", "System: Background processing (silent)", "Admin: 📥 New application received from User456. Added to review queue.", "System: Verification fails (silent)", "Bot: 🚫 Access Update (embed with generic denial message)", "Process complete - single clean embed only"], "embed_content": {"title": "🚫 Access Update", "color": "Red", "description": "Your access request has not been approved.", "fields": ["📝 Reason → Background verification could not be completed", "💡 Next Steps → If you believe this was a mistake or need clarification, please contact a staff member."]}}, "server_neutral_examples": {"description": "Examples showing server-neutral design", "use_cases": [{"server_type": "RP Server", "scenario": "Character application", "outcome": "Same clean embeds work perfectly"}, {"server_type": "Gaming Server", "scenario": "Team recruitment", "outcome": "Same clean embeds work perfectly"}, {"server_type": "Support Server", "scenario": "Help desk form", "outcome": "Same clean embeds work perfectly"}, {"server_type": "Business Server", "scenario": "Job application", "outcome": "Same clean embeds work perfectly"}]}}