{"streamlined_verification_scenarios": {"clean_dm_flow": {"description": "User with open DMs - Clean streamlined experience", "conversation": ["Bot: 📋 Server Application Form - Welcome embed with Start Form button", "User: *clicks Start Form*", "Bot: 📝 Question 1 of 3 - What is your name? (clean embed)", "User: <PERSON>", "Bot: 📝 Question 2 of 3 - What is your email? (no feedback, next question)", "User: <EMAIL>", "Bot: 📝 Question 3 of 3 - What is your phone? (no feedback, next question)", "User: 555-1234", "Bot: ✅ Your application has been accepted. You have been verified."], "backend_log": ["Q1: '<PERSON>' - Verified ✅", "Q2: '<EMAIL>' - Verified ✅", "Q3: '555-1234' - Verified ✅", "All verifications passed", "Verification role assigned", "Admin log: Complete verification details"]}, "clean_modal_flow": {"description": "User with closed DMs - Clean modal experience", "conversation": ["Bot: (in server) 📋 Server Application Form - DMs closed, using modal system", "User: *clicks Start Form*", "Bot: (modal) Question 1 of 3 - Text input modal", "User: *submits modal*", "Bot: (modal) Question 2 of 3 - Text input modal (no feedback)", "User: *submits modal*", "Bot: (modal) Question 3 of 3 - Text input modal (no feedback)", "User: *submits modal*", "Bot: (ephemeral) ✅ Your application has been accepted. You have been verified."], "backend_log": ["Q1: Answer verified silently ✅", "Q2: Answer verified silently ✅", "Q3: Answer verified silently ✅", "All verifications passed", "Verification role assigned", "Admin log: Complete verification details"]}, "verification_failure": {"description": "Required verification fails - Clean failure message", "conversation": ["Bot: 📝 Question 1 of 2 - What is your username?", "User: TestUser123", "Bot: 📝 Question 2 of 2 - What is your email? (no feedback)", "User: <EMAIL>", "Bot: ❌ Verification failed. Please try again or contact staff for help."], "backend_log": ["Q1: 'TestUser123' - Verified ✅", "Q2: '<EMAIL>' - Verification failed ❌ (required)", "Required verification failed", "No roles assigned", "Admin log: Verification failure details"]}}}