/**
 * Smart Role Assignment System
 * Handles intelligent role assignment with proper permissions, category inheritance, and audit logging
 */

const { PermissionsBitField, ChannelType, AuditLogEvent } = require('discord.js');

class SmartRoleAssignmentSystem {
    constructor() {
        this.auditLog = [];
        this.assignmentCache = new Map();
    }
    
    /**
     * Create private channel with intelligent role assignment
     * @param {Object} options - Channel creation options
     * @returns {Object} Result object with success status and details
     */
    async createPrivateChannelWithRole(options) {
        const {
            guild,
            channelName,
            role,
            channelType = 'text',
            categoryChannel = null,
            userId,
            sessionId,
            customPermissions = null
        } = options;
        
        try {
            // Validate inputs
            this.validateInputs(options);
            
            // Determine channel permissions
            const permissions = await this.calculateChannelPermissions(
                guild,
                role,
                channelType,
                categoryChannel,
                customPermissions
            );
            
            // Create the channel
            const channel = await this.createChannel(
                guild,
                channelName,
                channelType,
                categoryChannel,
                permissions
            );
            
            // Log the assignment
            this.logAssignment({
                type: 'channel_created',
                channelId: channel.id,
                channelName: channel.name,
                roleId: role.id,
                roleName: role.name,
                userId,
                sessionId,
                timestamp: Date.now(),
                permissions: permissions.map(p => ({
                    id: p.id,
                    type: p.type,
                    allow: p.allow?.toArray ? p.allow.toArray() : (Array.isArray(p.allow) ? p.allow : []),
                    deny: p.deny?.toArray ? p.deny.toArray() : (Array.isArray(p.deny) ? p.deny : [])
                }))
            });
            
            return {
                success: true,
                channel,
                role,
                permissions,
                auditId: this.auditLog.length - 1
            };
            
        } catch (error) {
            console.error('[SMART_ASSIGNMENT] Error creating private channel:', error);
            
            // Log the error
            this.logAssignment({
                type: 'channel_creation_failed',
                channelName,
                roleId: role?.id,
                roleName: role?.name,
                userId,
                sessionId,
                error: error.message,
                timestamp: Date.now()
            });
            
            return {
                success: false,
                error: error.message,
                auditId: this.auditLog.length - 1
            };
        }
    }
    
    /**
     * Create or update role with intelligent defaults
     * @param {Object} options - Role creation options
     * @returns {Object} Result object with success status and role
     */
    async createOrUpdateRole(options) {
        const {
            guild,
            roleName,
            roleOptions = {},
            existingRole = null,
            action = 'create', // 'create', 'update', 'replace'
            userId,
            sessionId
        } = options;
        
        try {
            let role;
            let actionTaken;
            
            switch (action) {
                case 'create':
                    role = await this.createNewRole(guild, roleName, roleOptions);
                    actionTaken = 'created';
                    break;
                    
                case 'update':
                    if (!existingRole) throw new Error('Existing role required for update action');
                    role = await this.updateExistingRole(existingRole, roleOptions);
                    actionTaken = 'updated';
                    break;
                    
                case 'replace':
                    if (!existingRole) throw new Error('Existing role required for replace action');
                    const membersToTransfer = existingRole.members.map(member => member.id);
                    role = await this.createNewRole(guild, roleName, roleOptions);
                    await this.transferRoleMembers(existingRole, role);
                    await existingRole.delete('Replaced by enhanced role matching system');
                    actionTaken = 'replaced';
                    break;
                    
                default:
                    throw new Error(`Unknown action: ${action}`);
            }
            
            // Log the role operation
            this.logAssignment({
                type: `role_${actionTaken}`,
                roleId: role.id,
                roleName: role.name,
                oldRoleId: existingRole?.id,
                oldRoleName: existingRole?.name,
                userId,
                sessionId,
                timestamp: Date.now(),
                roleOptions
            });
            
            return {
                success: true,
                role,
                action: actionTaken,
                auditId: this.auditLog.length - 1
            };
            
        } catch (error) {
            console.error('[SMART_ASSIGNMENT] Error with role operation:', error);
            
            this.logAssignment({
                type: `role_${action}_failed`,
                roleName,
                oldRoleId: existingRole?.id,
                userId,
                sessionId,
                error: error.message,
                timestamp: Date.now()
            });
            
            return {
                success: false,
                error: error.message,
                auditId: this.auditLog.length - 1
            };
        }
    }
    
    /**
     * Calculate optimal channel permissions
     */
    async calculateChannelPermissions(guild, role, channelType, categoryChannel, customPermissions) {
        const permissions = [];
        const everyoneRole = guild.roles.everyone;
        
        // Base permissions for @everyone (deny access)
        permissions.push({
            id: everyoneRole.id,
            type: 'role',
            deny: [PermissionsBitField.Flags.ViewChannel]
        });
        
        // Permissions for the assigned role
        const rolePermissions = this.getRolePermissions(channelType, customPermissions);
        permissions.push({
            id: role.id,
            type: 'role',
            allow: rolePermissions
        });
        
        // Inherit category permissions if applicable
        if (categoryChannel) {
            const inheritedPermissions = await this.inheritCategoryPermissions(
                categoryChannel,
                role,
                channelType
            );
            permissions.push(...inheritedPermissions);
        }
        
        // Add bot permissions to ensure functionality
        const botPermissions = this.getBotPermissions(guild, channelType);
        if (botPermissions) {
            permissions.push(botPermissions);
        }
        
        return permissions;
    }
    
    /**
     * Get appropriate permissions for role based on channel type
     */
    getRolePermissions(channelType, customPermissions) {
        if (customPermissions) {
            return Array.isArray(customPermissions) ? customPermissions : [customPermissions];
        }
        
        const basePermissions = [PermissionsBitField.Flags.ViewChannel];
        
        if (channelType === 'voice') {
            return [
                ...basePermissions,
                PermissionsBitField.Flags.Connect,
                PermissionsBitField.Flags.Speak,
                PermissionsBitField.Flags.UseVAD
            ];
        } else {
            return [
                ...basePermissions,
                PermissionsBitField.Flags.SendMessages,
                PermissionsBitField.Flags.ReadMessageHistory,
                PermissionsBitField.Flags.AddReactions,
                PermissionsBitField.Flags.UseExternalEmojis,
                PermissionsBitField.Flags.AttachFiles,
                PermissionsBitField.Flags.EmbedLinks
            ];
        }
    }
    
    /**
     * Inherit permissions from category channel
     */
    async inheritCategoryPermissions(categoryChannel, role, channelType) {
        const inheritedPermissions = [];
        
        // Get category permission overwrites
        const categoryOverwrites = categoryChannel.permissionOverwrites.cache;
        
        for (const [id, overwrite] of categoryOverwrites) {
            // Skip if it's the role we're already handling
            if (id === role.id) continue;
            
            // Only inherit certain types of overwrites
            if (overwrite.type === 'role' && this.shouldInheritPermission(overwrite)) {
                inheritedPermissions.push({
                    id: overwrite.id,
                    type: 'role',
                    allow: overwrite.allow,
                    deny: overwrite.deny
                });
            }
        }
        
        return inheritedPermissions;
    }
    
    /**
     * Determine if a permission should be inherited
     */
    shouldInheritPermission(overwrite) {
        // Inherit permissions for moderator roles, admin roles, etc.
        const allowedPermissions = [
            PermissionsBitField.Flags.ManageMessages,
            PermissionsBitField.Flags.ManageChannels,
            PermissionsBitField.Flags.MoveMembers,
            PermissionsBitField.Flags.MuteMembers,
            PermissionsBitField.Flags.DeafenMembers
        ];
        
        return allowedPermissions.some(perm => overwrite.allow.has(perm));
    }
    
    /**
     * Get bot permissions to ensure functionality
     */
    getBotPermissions(guild, channelType) {
        const botMember = guild.members.me;
        if (!botMember) return null;
        
        const botPermissions = [
            PermissionsBitField.Flags.ViewChannel,
            PermissionsBitField.Flags.ManageChannels,
            PermissionsBitField.Flags.ManageRoles
        ];
        
        if (channelType === 'voice') {
            botPermissions.push(
                PermissionsBitField.Flags.Connect,
                PermissionsBitField.Flags.MoveMembers
            );
        } else {
            botPermissions.push(
                PermissionsBitField.Flags.SendMessages,
                PermissionsBitField.Flags.ManageMessages,
                PermissionsBitField.Flags.ReadMessageHistory
            );
        }
        
        return {
            id: botMember.id,
            type: 'member',
            allow: botPermissions
        };
    }
    
    /**
     * Create the actual Discord channel
     */
    async createChannel(guild, channelName, channelType, categoryChannel, permissions) {
        const channelOptions = {
            name: channelName,
            type: channelType === 'voice' ? ChannelType.GuildVoice : ChannelType.GuildText,
            parent: categoryChannel?.id || null,
            permissionOverwrites: permissions.map(perm => ({
                id: perm.id,
                type: perm.type === 'role' ? 0 : 1,
                allow: perm.allow || [],
                deny: perm.deny || []
            }))
        };
        
        return await guild.channels.create(channelOptions);
    }
    
    /**
     * Create a new role with intelligent defaults
     */
    async createNewRole(guild, roleName, roleOptions) {
        const defaultOptions = {
            name: roleName,
            color: roleOptions.color || null,
            hoist: roleOptions.hoist || false,
            mentionable: roleOptions.mentionable || false,
            permissions: roleOptions.permissions || [],
            reason: 'Created by enhanced role matching system'
        };
        
        return await guild.roles.create(defaultOptions);
    }
    
    /**
     * Update an existing role
     */
    async updateExistingRole(role, roleOptions) {
        const updateOptions = {
            reason: 'Updated by enhanced role matching system'
        };
        
        if (roleOptions.color !== undefined) updateOptions.color = roleOptions.color;
        if (roleOptions.hoist !== undefined) updateOptions.hoist = roleOptions.hoist;
        if (roleOptions.mentionable !== undefined) updateOptions.mentionable = roleOptions.mentionable;
        if (roleOptions.permissions !== undefined) updateOptions.permissions = roleOptions.permissions;
        
        return await role.edit(updateOptions);
    }
    
    /**
     * Transfer members from old role to new role
     */
    async transferRoleMembers(oldRole, newRole) {
        const members = oldRole.members;
        const transferResults = [];
        
        for (const [memberId, member] of members) {
            try {
                await member.roles.add(newRole, 'Role replacement by enhanced role matching system');
                transferResults.push({ memberId, success: true });
            } catch (error) {
                console.error(`[SMART_ASSIGNMENT] Failed to transfer role for member ${memberId}:`, error);
                transferResults.push({ memberId, success: false, error: error.message });
            }
        }
        
        return transferResults;
    }
    
    /**
     * Validate input parameters
     */
    validateInputs(options) {
        const { guild, channelName, role, channelType } = options;
        
        if (!guild) throw new Error('Guild is required');
        if (!channelName || typeof channelName !== 'string') throw new Error('Valid channel name is required');
        if (!role) throw new Error('Role is required');
        if (channelType && !['text', 'voice'].includes(channelType)) {
            throw new Error('Channel type must be "text" or "voice"');
        }
        
        // Validate channel name
        if (channelName.length > 100) throw new Error('Channel name too long (max 100 characters)');
        if (!/^[a-zA-Z0-9\-_\s]+$/.test(channelName)) {
            throw new Error('Channel name contains invalid characters');
        }
    }
    
    /**
     * Log assignment operations for audit purposes
     */
    logAssignment(logEntry) {
        this.auditLog.push({
            id: this.auditLog.length,
            ...logEntry
        });
        
        // Keep only last 1000 entries to prevent memory issues
        if (this.auditLog.length > 1000) {
            this.auditLog.shift();
        }
        
        console.log(`[SMART_ASSIGNMENT] ${logEntry.type}:`, logEntry);
    }
    
    /**
     * Get audit log entries
     */
    getAuditLog(limit = 50, filter = null) {
        let entries = [...this.auditLog].reverse();
        
        if (filter) {
            entries = entries.filter(entry => {
                if (filter.type && entry.type !== filter.type) return false;
                if (filter.userId && entry.userId !== filter.userId) return false;
                if (filter.sessionId && entry.sessionId !== filter.sessionId) return false;
                if (filter.since && entry.timestamp < filter.since) return false;
                return true;
            });
        }
        
        return entries.slice(0, limit);
    }
    
    /**
     * Clear old audit log entries
     */
    cleanupAuditLog(maxAgeHours = 24) {
        const cutoff = Date.now() - (maxAgeHours * 60 * 60 * 1000);
        const originalLength = this.auditLog.length;
        
        this.auditLog = this.auditLog.filter(entry => entry.timestamp > cutoff);
        
        const removed = originalLength - this.auditLog.length;
        if (removed > 0) {
            console.log(`[SMART_ASSIGNMENT] Cleaned up ${removed} old audit log entries`);
        }
    }
    
    /**
     * Get assignment statistics
     */
    getStatistics(timeframe = 24) { // hours
        const since = Date.now() - (timeframe * 60 * 60 * 1000);
        const recentEntries = this.auditLog.filter(entry => entry.timestamp > since);
        
        const stats = {
            totalOperations: recentEntries.length,
            channelsCreated: recentEntries.filter(e => e.type === 'channel_created').length,
            rolesCreated: recentEntries.filter(e => e.type === 'role_created').length,
            rolesUpdated: recentEntries.filter(e => e.type === 'role_updated').length,
            rolesReplaced: recentEntries.filter(e => e.type === 'role_replaced').length,
            failures: recentEntries.filter(e => e.type.includes('failed')).length,
            successRate: 0
        };
        
        const successfulOps = stats.totalOperations - stats.failures;
        stats.successRate = stats.totalOperations > 0 ? (successfulOps / stats.totalOperations) * 100 : 0;
        
        return stats;
    }
}

module.exports = new SmartRoleAssignmentSystem();
