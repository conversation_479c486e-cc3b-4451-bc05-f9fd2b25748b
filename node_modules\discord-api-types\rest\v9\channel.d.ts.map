{"version": 3, "file": "channel.d.ts", "sourceRoot": "", "sources": ["channel.ts"], "names": [], "mappings": "AAAA,OAAO,KAAK,EAAE,WAAW,EAAE,SAAS,EAAE,MAAM,eAAe,CAAC;AAC5D,OAAO,KAAK,EACX,qBAAqB,EACrB,kBAAkB,EAClB,UAAU,EACV,QAAQ,EACR,iBAAiB,EACjB,kBAAkB,EAClB,UAAU,EACV,4BAA4B,EAC5B,mBAAmB,EACnB,aAAa,EACb,eAAe,EACf,OAAO,EACP,WAAW,EACX,gBAAgB,EAChB,YAAY,EACZ,aAAa,EACb,yBAAyB,EACzB,iBAAiB,EACjB,gBAAgB,EAChB,gBAAgB,EAChB,iCAAiC,EACjC,aAAa,EACb,eAAe,EACf,YAAY,EACZ,aAAa,EACb,MAAM,yBAAyB,CAAC;AACjC,OAAO,KAAK,EAAE,qDAAqD,EAAE,cAAc,EAAE,MAAM,uBAAuB,CAAC;AACnH,OAAO,KAAK,EAAE,WAAW,EAAE,MAAM,QAAQ,CAAC;AAE1C,MAAM,WAAW,4BAA6B,SAAQ,mCAAmC;IACxF,EAAE,EAAE,SAAS,CAAC;CACd;AAED;;GAEG;AACH,MAAM,MAAM,wBAAwB,GAAG,4BAA4B,CAAC;AAEpE;;GAEG;AACH,MAAM,MAAM,uBAAuB,GAAG,UAAU,CAAC;AAEjD;;GAEG;AACH,MAAM,WAAW,2BAA2B;IAC3C;;;;OAIG;IACH,IAAI,CAAC,EAAE,MAAM,GAAG,SAAS,CAAC;IAE1B;;;;;OAKG;IACH,IAAI,CAAC,EAAE,WAAW,CAAC,iBAAiB,GAAG,WAAW,CAAC,SAAS,GAAG,SAAS,CAAC;IACzE;;;;OAIG;IACH,QAAQ,CAAC,EAAE,MAAM,GAAG,IAAI,GAAG,SAAS,CAAC;IACrC;;;;OAIG;IACH,KAAK,CAAC,EAAE,MAAM,GAAG,IAAI,GAAG,SAAS,CAAC;IAClC;;;;OAIG;IACH,IAAI,CAAC,EAAE,OAAO,GAAG,IAAI,GAAG,SAAS,CAAC;IAClC;;;;;;OAMG;IACH,mBAAmB,CAAC,EAAE,MAAM,GAAG,IAAI,GAAG,SAAS,CAAC;IAChD;;;;OAIG;IACH,OAAO,CAAC,EAAE,MAAM,GAAG,IAAI,GAAG,SAAS,CAAC;IACpC;;;;OAIG;IACH,UAAU,CAAC,EAAE,MAAM,GAAG,IAAI,GAAG,SAAS,CAAC;IACvC;;;;OAIG;IACH,qBAAqB,CAAC,EAAE,wBAAwB,EAAE,GAAG,IAAI,GAAG,SAAS,CAAC;IACtE;;;;OAIG;IACH,SAAS,CAAC,EAAE,SAAS,GAAG,IAAI,GAAG,SAAS,CAAC;IACzC;;;;OAIG;IACH,UAAU,CAAC,EAAE,MAAM,GAAG,IAAI,GAAG,SAAS,CAAC;IACvC;;;;OAIG;IACH,kBAAkB,CAAC,EAAE,gBAAgB,GAAG,IAAI,GAAG,SAAS,CAAC;IACzD;;;;OAIG;IACH,QAAQ,CAAC,EAAE,OAAO,GAAG,SAAS,CAAC;IAC/B;;;;OAIG;IACH,qBAAqB,CAAC,EAAE,yBAAyB,GAAG,SAAS,CAAC;IAC9D;;;;OAIG;IACH,MAAM,CAAC,EAAE,OAAO,GAAG,SAAS,CAAC;IAC7B;;;;OAIG;IACH,6BAA6B,CAAC,EAAE,yBAAyB,GAAG,SAAS,CAAC;IACtE;;OAEG;IACH,KAAK,CAAC,EAAE,YAAY,GAAG,SAAS,CAAC;IACjC;;;;OAIG;IACH,cAAc,CAAC,EAAE,CAAC,OAAO,CAAC,gBAAgB,CAAC,GAAG,IAAI,CAAC,gBAAgB,EAAE,MAAM,CAAC,CAAC,EAAE,GAAG,SAAS,CAAC;IAC5F;;;;OAIG;IACH,SAAS,CAAC,EAAE,OAAO,GAAG,SAAS,CAAC;IAChC;;;;OAIG;IACH,sBAAsB,CAAC,EAAE,iCAAiC,GAAG,SAAS,CAAC;IACvE;;;;;OAKG;IACH,kCAAkC,CAAC,EAAE,MAAM,GAAG,IAAI,GAAG,SAAS,CAAC;IAC/D;;;;OAIG;IACH,kBAAkB,CAAC,EAAE,aAAa,GAAG,IAAI,GAAG,SAAS,CAAC;IACtD;;;;OAIG;IACH,oBAAoB,CAAC,EAAE,eAAe,GAAG,SAAS,CAAC;IACnD;;;;OAIG;IACH,YAAY,CAAC,EAAE,SAAS,EAAE,GAAG,SAAS,CAAC;CACvC;AAED;;GAEG;AACH,MAAM,MAAM,yBAAyB,GAAG,UAAU,CAAC;AAEnD;;GAEG;AACH,MAAM,MAAM,0BAA0B,GAAG,UAAU,CAAC;AAEpD;;GAEG;AACH,MAAM,WAAW,8BAA8B;IAC9C;;OAEG;IACH,MAAM,CAAC,EAAE,SAAS,CAAC;IACnB;;OAEG;IACH,MAAM,CAAC,EAAE,SAAS,CAAC;IACnB;;OAEG;IACH,KAAK,CAAC,EAAE,SAAS,CAAC;IAClB;;;;OAIG;IACH,KAAK,CAAC,EAAE,MAAM,CAAC;CACf;AAED;;GAEG;AACH,MAAM,MAAM,+BAA+B,GAAG,UAAU,EAAE,CAAC;AAE3D;;GAEG;AACH,MAAM,MAAM,8BAA8B,GAAG,UAAU,CAAC;AAExD;;GAEG;AACH,MAAM,MAAM,uBAAuB,GAAG,qDAAqD,CAC1F,QAAQ,CAAC,IAAI,CAAC,mBAAmB,EAAE,YAAY,CAAC,CAAC,CACjD,GACA,cAAc,CAAC,mBAAmB,CAAC,GAAG;IACrC;;;;OAIG;IACH,kBAAkB,CAAC,EAAE,OAAO,GAAG,SAAS,CAAC;CACzC,CAAC;AAEH;;GAEG;AACH,MAAM,MAAM,uBAAuB,GAAG,uBAAuB,CAAC;AAE9D;;GAEG;AACH,MAAM,MAAM,iBAAiB,GAAG,OAAO,CACtC,IAAI,CAAC,aAAa,EAAE,aAAa,GAAG,eAAe,GAAG,UAAU,GAAG,OAAO,GAAG,UAAU,CAAC,CACxF,GAAG;IACH;;OAEG;IACH,EAAE,EAAE,SAAS,GAAG,MAAM,CAAC;CACvB,CAAC;AAEF;;GAEG;AACH,MAAM,WAAW,iCAAiC;IACjD;;OAEG;IACH,OAAO,CAAC,EAAE,MAAM,GAAG,SAAS,CAAC;IAC7B;;OAEG;IACH,KAAK,CAAC,EAAE,MAAM,GAAG,MAAM,GAAG,SAAS,CAAC;IACpC;;OAEG;IACH,GAAG,CAAC,EAAE,OAAO,GAAG,SAAS,CAAC;IAC1B;;;;OAIG;IACH,MAAM,CAAC,EAAE,QAAQ,EAAE,GAAG,SAAS,CAAC;IAChC;;;;;OAKG;IACH,KAAK,CAAC,EAAE,QAAQ,GAAG,SAAS,CAAC;IAC7B;;;;OAIG;IACH,gBAAgB,CAAC,EAAE,kBAAkB,GAAG,SAAS,CAAC;IAClD;;;;OAIG;IACH,iBAAiB,CAAC,EAAE,uBAAuB,GAAG,SAAS,CAAC;IACxD;;;;OAIG;IACH,UAAU,CAAC,EAAE,qBAAqB,CAAC,4BAA4B,CAAC,EAAE,GAAG,SAAS,CAAC;IAC/E;;;;OAIG;IACH,WAAW,CAAC,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC,GAAG,CAAC,SAAS,EAAE,SAAS,CAAC,GAAG,CAAC,SAAS,CAAC,GAAG,SAAS,CAAC;IACnG;;OAEG;IACH,WAAW,CAAC,EAAE,iBAAiB,EAAE,GAAG,SAAS,CAAC;IAC9C;;OAEG;IACH,KAAK,CAAC,EAAE,YAAY,GAAG,SAAS,CAAC;IACjC;;;OAGG;IACH,aAAa,CAAC,EAAE,OAAO,GAAG,SAAS,CAAC;IACpC;;OAEG;IACH,IAAI,CAAC,EAAE,WAAW,GAAG,SAAS,CAAC;CAC/B;AAED;;GAEG;AACH,MAAM,MAAM,qCAAqC,GAC9C,CAAC,MAAM,CAAC,SAAS,MAAM,GAAG,EAAE,OAAO,CAAC,GAAG;IACvC;;OAEG;IACH,YAAY,CAAC,EAAE,MAAM,GAAG,SAAS,CAAC;CACjC,CAAC,GACF,CAAC,MAAM,CAAC,SAAS,MAAM,GAAG,EAAE,OAAO,CAAC,GAAG,iCAAiC,CAAC,CAAC;AAE7E;;GAEG;AACH,MAAM,MAAM,+BAA+B,GAAG,UAAU,CAAC;AAEzD;;GAEG;AACH,MAAM,MAAM,wCAAwC,GAAG,UAAU,CAAC;AAElE;;GAEG;AACH,MAAM,MAAM,sCAAsC,GAAG,KAAK,CAAC;AAE3D;;GAEG;AACH,MAAM,MAAM,4CAA4C,GAAG,KAAK,CAAC;AAEjE;;GAEG;AACH,MAAM,MAAM,sCAAsC,GAAG,4CAA4C,CAAC;AAElG;;GAEG;AACH,MAAM,MAAM,6CAA6C,GAAG,KAAK,CAAC;AAElE;;GAEG;AACH,MAAM,WAAW,0CAA0C;IAC1D;;OAEG;IACH,IAAI,CAAC,EAAE,YAAY,CAAC;IACpB;;OAEG;IACH,KAAK,CAAC,EAAE,SAAS,CAAC;IAClB;;;;OAIG;IACH,KAAK,CAAC,EAAE,MAAM,CAAC;CACf;AAED;;GAEG;AACH,oBAAY,YAAY;IACvB,MAAM,IAAA;IACN,KAAK,IAAA;CACL;AAED;;GAEG;AACH,MAAM,MAAM,2CAA2C,GAAG,OAAO,EAAE,CAAC;AAEpE;;GAEG;AACH,MAAM,MAAM,6CAA6C,GAAG,KAAK,CAAC;AAElE;;GAEG;AACH,MAAM,MAAM,yCAAyC,GAAG,KAAK,CAAC;AAE9D;;GAEG;AACH,MAAM,WAAW,kCAAkC;IAClD;;OAEG;IACH,OAAO,CAAC,EAAE,MAAM,GAAG,IAAI,GAAG,SAAS,CAAC;IACpC;;;;OAIG;IACH,MAAM,CAAC,EAAE,QAAQ,EAAE,GAAG,IAAI,GAAG,SAAS,CAAC;IACvC;;;;;OAKG;IACH,KAAK,CAAC,EAAE,QAAQ,GAAG,IAAI,GAAG,SAAS,CAAC;IACpC;;;;;;;OAOG;IACH,KAAK,CAAC,EAAE,YAAY,GAAG,IAAI,GAAG,SAAS,CAAC;IACxC;;;;OAIG;IACH,gBAAgB,CAAC,EAAE,kBAAkB,GAAG,IAAI,GAAG,SAAS,CAAC;IACzD;;;;;;OAMG;IACH,WAAW,CAAC,EAAE,iBAAiB,EAAE,GAAG,SAAS,CAAC;IAC9C;;;;OAIG;IACH,UAAU,CAAC,EAAE,qBAAqB,CAAC,4BAA4B,CAAC,EAAE,GAAG,IAAI,GAAG,SAAS,CAAC;CACtF;AAED;;GAEG;AACH,MAAM,MAAM,sCAAsC,GAC/C,CAAC,MAAM,CAAC,SAAS,MAAM,GAAG,EAAE,OAAO,CAAC,GAAG;IACvC;;OAEG;IACH,YAAY,CAAC,EAAE,MAAM,GAAG,SAAS,CAAC;CACjC,CAAC,GACF,CAAC,MAAM,CAAC,SAAS,MAAM,GAAG,EAAE,OAAO,CAAC,GAAG,kCAAkC,CAAC,CAAC;AAE9E;;GAEG;AACH,MAAM,MAAM,gCAAgC,GAAG,UAAU,CAAC;AAE1D;;GAEG;AACH,MAAM,MAAM,iCAAiC,GAAG,KAAK,CAAC;AAEtD;;GAEG;AACH,MAAM,WAAW,4CAA4C;IAC5D;;OAEG;IACH,QAAQ,EAAE,SAAS,EAAE,CAAC;CACtB;AAED;;GAEG;AACH,MAAM,MAAM,0CAA0C,GAAG,KAAK,CAAC;AAE/D;;GAEG;AACH,MAAM,WAAW,mCAAmC;IACnD;;;;;OAKG;IACH,KAAK,CAAC,EAAE,WAAW,GAAG,IAAI,GAAG,SAAS,CAAC;IACvC;;;;;OAKG;IACH,IAAI,CAAC,EAAE,WAAW,GAAG,IAAI,GAAG,SAAS,CAAC;IACtC;;OAEG;IACH,IAAI,EAAE,aAAa,CAAC;CACpB;AAED;;GAEG;AACH,MAAM,MAAM,iCAAiC,GAAG,KAAK,CAAC;AAEtD;;GAEG;AACH,MAAM,MAAM,8BAA8B,GAAG,iBAAiB,EAAE,CAAC;AAEjE;;GAEG;AACH,MAAM,WAAW,gCAAgC;IAChD;;;;OAIG;IACH,OAAO,CAAC,EAAE,MAAM,GAAG,SAAS,CAAC;IAC7B;;;;OAIG;IACH,QAAQ,CAAC,EAAE,MAAM,GAAG,SAAS,CAAC;IAC9B;;;;OAIG;IACH,SAAS,CAAC,EAAE,OAAO,GAAG,SAAS,CAAC;IAChC;;;;;OAKG;IACH,MAAM,CAAC,EAAE,OAAO,GAAG,SAAS,CAAC;IAC7B;;;;OAIG;IACH,WAAW,CAAC,EAAE,gBAAgB,GAAG,SAAS,CAAC;IAC3C;;;;OAIG;IACH,cAAc,CAAC,EAAE,SAAS,GAAG,SAAS,CAAC;IACvC;;;;OAIG;IACH,qBAAqB,CAAC,EAAE,SAAS,GAAG,SAAS,CAAC;CAC9C;AAED;;GAEG;AACH,MAAM,MAAM,8BAA8B,GAAG,iBAAiB,CAAC;AAE/D;;GAEG;AACH,MAAM,MAAM,oCAAoC,GAAG,KAAK,CAAC;AAEzD;;GAEG;AACH,MAAM,WAAW,mCAAmC;IACnD;;OAEG;IACH,kBAAkB,EAAE,SAAS,CAAC;CAC9B;AAED;;GAEG;AACH,MAAM,MAAM,iCAAiC,GAAG,kBAAkB,CAAC;AAEnE;;GAEG;AACH,MAAM,MAAM,8BAA8B,GAAG,KAAK,CAAC;AAEnD;;GAEG;AACH,MAAM,MAAM,2BAA2B,GAAG,UAAU,EAAE,CAAC;AAEvD;;GAEG;AACH,MAAM,MAAM,0BAA0B,GAAG,KAAK,CAAC;AAE/C;;GAEG;AACH,MAAM,MAAM,6BAA6B,GAAG,KAAK,CAAC;AAElD;;GAEG;AACH,MAAM,WAAW,kCAAkC;IAClD;;OAEG;IACH,YAAY,EAAE,MAAM,CAAC;IACrB;;OAEG;IACH,IAAI,CAAC,EAAE,MAAM,GAAG,SAAS,CAAC;CAC1B;AAED;;GAEG;AACH,MAAM,MAAM,gCAAgC,GAAG,OAAO,CAAC;AAEvD;;GAEG;AACH,MAAM,MAAM,mCAAmC,GAAG,OAAO,CAAC;AAE1D;;GAEG;AACH,MAAM,WAAW,yCAAyC;IACzD;;OAEG;IACH,IAAI,EAAE,MAAM,CAAC;IACb;;OAEG;IACH,qBAAqB,CAAC,EAAE,yBAAyB,GAAG,SAAS,CAAC;IAC9D;;OAEG;IACH,mBAAmB,CAAC,EAAE,MAAM,GAAG,SAAS,CAAC;CACzC;AAED;;GAEG;AACH,MAAM,MAAM,oCAAoC,GAAG,yCAAyC,GAAG;IAC9F;;OAEG;IACH,OAAO,EAAE,iCAAiC,CAAC;IAC3C;;OAEG;IACH,YAAY,CAAC,EAAE,SAAS,EAAE,GAAG,SAAS,CAAC;CACvC,CAAC;AAEF;;GAEG;AACH,MAAM,MAAM,wCAAwC,GAAG,yCAAyC,GAAG;IAClG;;OAEG;IACH,OAAO,EAAE,MAAM,CAAC;CAChB,CAAC;AAEF;;GAEG;AACH,MAAM,MAAM,uCAAuC,GAAG,UAAU,CAAC;AAEjE;;GAEG;AACH,MAAM,WAAW,iCAAkC,SAAQ,yCAAyC;IACnG;;;;;;;;OAQG;IACH,IAAI,CAAC,EAAE,iBAAiB,GAAG,SAAS,CAAC;IACrC;;OAEG;IACH,SAAS,CAAC,EAAE,OAAO,GAAG,SAAS,CAAC;CAChC;AAED;;GAEG;AACH,MAAM,MAAM,+BAA+B,GAAG,UAAU,CAAC;AAEzD;;GAEG;AACH,MAAM,MAAM,oCAAoC,GAAG,KAAK,CAAC;AAEzD;;GAEG;AACH,MAAM,MAAM,uCAAuC,GAAG,KAAK,CAAC;AAE5D;;GAEG;AACH,MAAM,WAAW,kCAAkC;IAClD;;OAEG;IACH,WAAW,CAAC,EAAE,OAAO,CAAC;CACtB;AAED;;GAEG;AACH,MAAM,MAAM,mCAAmC,GAAG,eAAe,CAAC;AAElE;;GAEG;AACH,MAAM,WAAW,mCAAmC;IACnD;;OAEG;IACH,WAAW,CAAC,EAAE,OAAO,CAAC;IACtB;;OAEG;IACH,KAAK,CAAC,EAAE,SAAS,CAAC;IAClB;;OAEG;IACH,KAAK,CAAC,EAAE,MAAM,CAAC;CACf;AAED;;GAEG;AACH,MAAM,MAAM,oCAAoC,GAAG,eAAe,EAAE,CAAC;AAErE;;GAEG;AACH,MAAM,WAAW,qCAAqC;IACrD;;OAEG;IACH,MAAM,CAAC,EAAE,SAAS,GAAG,MAAM,CAAC;IAC5B;;OAEG;IACH,KAAK,CAAC,EAAE,MAAM,CAAC;CACf;AAED;;GAEG;AACH,MAAM,MAAM,4CAA4C,GAAG,2CAA2C,CAAC;AAEvG;;GAEG;AACH,MAAM,MAAM,6CAA6C,GAAG,2CAA2C,CAAC;AAExG;;;GAGG;AACH,MAAM,MAAM,8BAA8B,GAAG,aAAa,CAAC;AAE3D;;GAEG;AACH,MAAM,MAAM,2CAA2C,GAAG,aAAa,CAAC"}