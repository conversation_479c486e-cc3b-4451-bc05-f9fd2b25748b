const {
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    ActionRow<PERSON><PERSON>er,
    <PERSON><PERSON><PERSON><PERSON>er,
    ButtonStyle,
    MessageFlags
} = require('discord.js');

class RoleMatchingHandler {
    /**
     * Perform fuzzy matching on role names
     * @param {string} intendedRoleName - The name of the role to be created
     * @param {Collection} existingRoles - Collection of existing guild roles
     * @returns {Array} Array of potential matches
     */
    findFuzzyMatches(intendedRoleName, existingRoles) {
        const matches = [];
        const intendedWords = this.splitRoleName(intendedRoleName.toLowerCase());
        
        if (intendedWords.length === 0) return matches;
        
        const firstWord = intendedWords[0];
        
        existingRoles.forEach(role => {
            // Skip @everyone and managed roles
            if (role.name === '@everyone' || role.managed) return;
            
            const existingWords = this.splitRoleName(role.name.toLowerCase());
            if (existingWords.length === 0) return;
            
            const existingFirstWord = existingWords[0];
            
            // Check if first words match
            if (firstWord === existingFirstWord) {
                matches.push({
                    role: role,
                    matchType: 'exact_first_word',
                    confidence: 1.0
                });
            }
            // Check for partial matches (first word contains or is contained)
            else if (firstWord.includes(existingFirstWord) || existingFirstWord.includes(firstWord)) {
                const confidence = Math.max(firstWord.length, existingFirstWord.length) / 
                                Math.min(firstWord.length, existingFirstWord.length);
                matches.push({
                    role: role,
                    matchType: 'partial_first_word',
                    confidence: 1 / confidence
                });
            }
        });
        
        // Sort by confidence (highest first)
        matches.sort((a, b) => b.confidence - a.confidence);
        
        // Return top 3 matches to avoid overwhelming the user
        return matches.slice(0, 3);
    }
    
    /**
     * Split role name by common separators
     * @param {string} roleName - The role name to split
     * @returns {Array} Array of words/segments
     */
    splitRoleName(roleName) {
        return roleName
            .split(/[\s\-_]+/)
            .filter(word => word.length > 0)
            .map(word => word.trim());
    }
    
    /**
     * Create interactive role selection embed and components
     * @param {string} intendedRoleName - The intended role name
     * @param {string} channelName - The channel name that triggered this
     * @param {Array} matches - Array of fuzzy matches
     * @param {string} sessionId - Unique session ID for this interaction
     * @returns {Object} Object containing embed and components
     */
    createRoleSelectionInterface(intendedRoleName, channelName, matches, sessionId) {
        const embed = new EmbedBuilder()
            .setTitle('🔍 Similar Role Detected')
            .setColor(0xFFA500) // Orange color for attention
            .setDescription(
                `**Channel:** ${channelName}\n` +
                `**Intended Role:** \`${intendedRoleName}\`\n\n` +
                `I found ${matches.length} similar role${matches.length > 1 ? 's' : ''} that might match what you're trying to create:\n\n` +
                matches.map((match, index) => 
                    `**${index + 1}.** ${match.role} (ID: \`${match.role.id}\`)\n` +
                    `   └ Created: <t:${Math.floor(match.role.createdTimestamp / 1000)}:R>\n` +
                    `   └ Members: ${match.role.members.size}\n` +
                    `   └ Match: ${match.matchType === 'exact_first_word' ? 'Exact first word' : 'Partial first word'}`
                ).join('\n\n') +
                `\n\n**What would you like to do?**`
            )
            .setFooter({ 
                text: 'This decision will affect role management for your server. Choose carefully.' 
            });
        
        const components = [];
        
        // First row: Main action buttons
        const mainRow = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId(`role_match_use_${sessionId}`)
                    .setLabel('Use Existing Role')
                    .setStyle(ButtonStyle.Success)
                    .setEmoji('✅'),
                new ButtonBuilder()
                    .setCustomId(`role_match_create_${sessionId}`)
                    .setLabel('Create New Role')
                    .setStyle(ButtonStyle.Primary)
                    .setEmoji('➕'),
                new ButtonBuilder()
                    .setCustomId(`role_match_replace_${sessionId}`)
                    .setLabel('Replace Existing')
                    .setStyle(ButtonStyle.Danger)
                    .setEmoji('🔄')
            );
        
        components.push(mainRow);
        
        // Second row: Cancel button
        const cancelRow = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId(`role_match_cancel_${sessionId}`)
                    .setLabel('Cancel Operation')
                    .setStyle(ButtonStyle.Secondary)
                    .setEmoji('❌')
            );
        
        components.push(cancelRow);
        
        return { embed, components };
    }
    
    /**
     * Create detailed explanation embed for each action
     * @param {string} action - The action type (use, create, replace)
     * @param {string} intendedRoleName - The intended role name
     * @param {Array} matches - Array of fuzzy matches
     * @returns {EmbedBuilder} Explanation embed
     */
    createActionExplanationEmbed(action, intendedRoleName, matches) {
        const embed = new EmbedBuilder();
        
        switch (action) {
            case 'use':
                embed
                    .setTitle('✅ Using Existing Role')
                    .setColor(0x00FF00)
                    .setDescription(
                        `**Selected Action:** Use existing role for the new channel\n\n` +
                        `**What will happen:**\n` +
                        `• The new channel will use the existing role: ${matches[0].role}\n` +
                        `• No new role will be created\n` +
                        `• Members who already have this role will have access to the new channel\n` +
                        `• The existing role remains unchanged\n\n` +
                        `**Result:** Channel created with existing role permissions`
                    );
                break;
                
            case 'create':
                embed
                    .setTitle('➕ Creating New Role')
                    .setColor(0x0099FF)
                    .setDescription(
                        `**Selected Action:** Create new role as originally intended\n\n` +
                        `**What will happen:**\n` +
                        `• A new role named \`${intendedRoleName}\` will be created\n` +
                        `• The new channel will use this new role\n` +
                        `• Both the existing role(s) and new role will coexist\n` +
                        `• You'll have separate roles for different purposes\n\n` +
                        `**Result:** New role and channel created, existing roles unchanged`
                    );
                break;
                
            case 'replace':
                embed
                    .setTitle('🔄 Replacing Existing Role')
                    .setColor(0xFF6600)
                    .setDescription(
                        `**Selected Action:** Replace existing role with new one\n\n` +
                        `**What will happen:**\n` +
                        `• The existing role ${matches[0].role} will be deleted\n` +
                        `• A new role named \`${intendedRoleName}\` will be created\n` +
                        `• Members who had the old role will lose it\n` +
                        `• The new channel will use the new role\n\n` +
                        `**⚠️ Warning:** This action cannot be undone!\n` +
                        `**Result:** Old role deleted, new role and channel created`
                    );
                break;
        }
        
        return embed;
    }
    
    /**
     * Store role matching session data
     * @param {string} sessionId - Unique session ID
     * @param {Object} sessionData - Session data to store
     */
    storeMatchingSession(sessionId, sessionData) {
        if (!global.roleMatchingSessions) {
            global.roleMatchingSessions = new Map();
        }
        
        global.roleMatchingSessions.set(sessionId, {
            ...sessionData,
            timestamp: Date.now()
        });
    }
    
    /**
     * Get role matching session data
     * @param {string} sessionId - Session ID to retrieve
     * @returns {Object|null} Session data or null if not found
     */
    getMatchingSession(sessionId) {
        if (!global.roleMatchingSessions) {
            return null;
        }
        
        return global.roleMatchingSessions.get(sessionId);
    }
    
    /**
     * Delete role matching session data
     * @param {string} sessionId - Session ID to delete
     */
    deleteMatchingSession(sessionId) {
        if (global.roleMatchingSessions) {
            global.roleMatchingSessions.delete(sessionId);
        }
    }
    
    /**
     * Clean up expired matching sessions
     */
    cleanupExpiredSessions() {
        if (!global.roleMatchingSessions) return;
        
        const now = Date.now();
        const expiredKeys = [];
        
        for (const [sessionId, data] of global.roleMatchingSessions.entries()) {
            // Remove sessions older than 10 minutes
            if (now - data.timestamp > 10 * 60 * 1000) {
                expiredKeys.push(sessionId);
            }
        }
        
        expiredKeys.forEach(sessionId => {
            global.roleMatchingSessions.delete(sessionId);
            console.log(`[ROLE_MATCHING] Cleaned up expired session: ${sessionId}`);
        });
    }
    
    /**
     * Generate a unique session ID
     * @param {string} userId - User ID
     * @param {string} guildId - Guild ID
     * @returns {string} Unique session ID
     */
    generateSessionId(userId, guildId) {
        return `${userId}_${guildId}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }
}

// Set up cleanup interval for expired sessions
setInterval(() => {
    const handler = new RoleMatchingHandler();
    handler.cleanupExpiredSessions();
}, 5 * 60 * 1000); // Clean up every 5 minutes

module.exports = new RoleMatchingHandler();