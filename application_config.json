{"656611572021592064": {"enabled": false, "questions": [], "applicationTypes": {"app_1753531375649_37209mr6p": {"name": "test", "description": "test", "questions": [{"question": "name", "type": "text", "required": true, "placeholder": null, "maxLength": 1000, "multiline": true, "verifyInChannels": false, "verificationMode": "none", "logVerification": false}, {"question": "email", "type": "text", "required": true, "placeholder": null, "maxLength": 1000, "multiline": true, "verifyInChannels": true, "verificationMode": "required", "logVerification": false}], "messages": {"success": {"title": "✅ test Approved!", "description": "Your application has been approved.", "footer": null}, "denial": {"title": "❌ test Denied", "description": "Your application was not approved at this time.", "footer": null}, "submission": {"title": "📄 Application Summary", "description": "Here is a summary of your submitted application.", "footer": "Thank you for your submission!"}}, "roleAssignments": {}, "reviewChannelId": null, "createdAt": "2025-07-26T12:02:55.649Z"}}, "defaultApplicationType": "app_1753531375649_37209mr6p", "roleAssignments": {}, "applicationChannelId": null, "logChannelId": null, "adminChannelId": null, "applicationPanelMessageId": null, "verificationChannelIds": [], "logApplicationVerification": false, "messages": {"success": {"title": "✅ Your application has been accepted.", "description": "Thank you for completing the application process.", "footer": null}, "denial": {"title": "❌ Verification failed. Please try again or contact staff for help.", "description": "Your application could not be processed at this time.", "footer": null}, "submission": {"title": "📄 Application Summary", "description": "Here is a summary of your submitted application.", "footer": "Thank you for your submission!"}, "revoke": {"title": "🚫 Access Revoked", "description": "Your access has been revoked.", "footer": "If you believe this was done in error, please contact the server staff."}}}}