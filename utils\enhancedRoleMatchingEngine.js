/**
 * Enhanced Role Matching Engine
 * Provides comprehensive role matching with multiple algorithms and configurable thresholds
 */

const roleMatchingConfig = require('./roleMatchingConfig');
const roleNameNormalizer = require('./roleNameNormalizer');

class EnhancedRoleMatchingEngine {
    constructor() {
        this.matchCache = new Map();
        this.cacheTimestamps = new Map();
    }
    
    /**
     * Find matching roles using multiple algorithms
     * @param {string} intendedRoleName - The name of the role to be created
     * @param {Collection} existingRoles - Collection of existing guild roles
     * @param {string} guildId - Discord guild ID for configuration
     * @returns {Array} Array of potential matches with scores
     */
    findMatches(intendedRoleName, existingRoles, guildId) {
        const config = roleMatchingConfig.getConfig(guildId);
        
        // Check cache first
        const cacheKey = `${guildId}_${intendedRoleName.toLowerCase()}`;
        const cached = this.getFromCache(cacheKey, config.cacheResultsMinutes);
        if (cached) {
            return cached;
        }
        
        const matches = [];

        console.log(`[ENHANCED_MATCHING] Finding fuzzy matches for "${intendedRoleName}"`);

        // Filter roles based on configuration
        const filteredRoles = this.filterRoles(existingRoles, config);

        // Limit roles to scan for performance
        const rolesToScan = Array.from(filteredRoles.values()).slice(0, config.maxRolesToScan);

        console.log(`[ENHANCED_MATCHING] Scanning ${rolesToScan.length} roles with fuzzy normalization`);

        for (const role of rolesToScan) {
            // Use the new fuzzy normalizer for intelligent matching
            const similarity = roleNameNormalizer.calculateSimilarity(intendedRoleName, role.name);

            // Enhanced threshold logic for channel name matches
            let threshold = config.minimumMatchThreshold || config.similarityThreshold || 70;

            // Lower threshold for high-quality channel name matches
            if (similarity.details && similarity.details.channelNameScore >= 75) {
                threshold = Math.min(threshold, 70); // Allow channel name matches at 70%
                console.log(`[ENHANCED_MATCHING] Channel name pattern detected, using lower threshold: ${threshold}%`);
            }

            if (similarity.score >= threshold) {
                matches.push({
                    role: role,
                    score: similarity.score,
                    matchType: similarity.matchType,
                    algorithm: 'fuzzy_normalized',
                    confidence: similarity.score / 100,
                    reason: similarity.reason,
                    normalizedIntended: similarity.normalized1,
                    normalizedExisting: similarity.normalized2,
                    details: similarity.details || {},
                    isChannelNameMatch: similarity.details && similarity.details.channelNameScore >= 75
                });

                console.log(`[ENHANCED_MATCHING] Fuzzy match: "${role.name}" -> ${similarity.score}% (${similarity.matchType})${similarity.details && similarity.details.channelNameScore >= 75 ? ' [CHANNEL_NAME_MATCH]' : ''}`);
            }
        }

        // Enhanced sorting: prioritize exact matches, then channel name matches, then by score
        matches.sort((a, b) => {
            // Exact matches first
            if (a.matchType === 'exact' && b.matchType !== 'exact') return -1;
            if (b.matchType === 'exact' && a.matchType !== 'exact') return 1;

            // Channel name matches second (if both are not exact)
            if (a.matchType !== 'exact' && b.matchType !== 'exact') {
                if (a.isChannelNameMatch && !b.isChannelNameMatch) return -1;
                if (b.isChannelNameMatch && !a.isChannelNameMatch) return 1;
            }

            // Then by score
            return b.score - a.score;
        });
        const limitedMatches = matches.slice(0, config.maxMatchesToDisplay);
        
        // Cache the result
        this.cacheResult(cacheKey, limitedMatches);
        
        return limitedMatches;
    }
    
    /**
     * Calculate match score using multiple algorithms
     * @param {string} intended - Normalized intended role name
     * @param {string} existing - Normalized existing role name
     * @param {Array} intendedWords - Words from intended name
     * @param {Array} existingWords - Words from existing name
     * @param {Object} config - Configuration object
     * @returns {Object} Match result with score and details
     */
    calculateMatchScore(intended, existing, intendedWords, existingWords, config) {
        const algorithms = config.enabledAlgorithms;
        const weights = config.algorithmWeights;
        const scores = {};
        let totalWeight = 0;
        let weightedScore = 0;
        
        // Exact match algorithm
        if (algorithms.exact) {
            scores.exact = this.exactMatch(intended, existing);
            if (scores.exact >= config.exactMatchThreshold) {
                return {
                    score: scores.exact,
                    type: 'exact',
                    details: 'Exact name match (case-insensitive)',
                    algorithms: { exact: scores.exact }
                };
            }
            weightedScore += scores.exact * weights.exact;
            totalWeight += weights.exact;
        }
        
        // Levenshtein distance algorithm
        if (algorithms.levenshtein) {
            scores.levenshtein = this.levenshteinMatch(intended, existing);
            weightedScore += scores.levenshtein * weights.levenshtein;
            totalWeight += weights.levenshtein;
        }
        
        // Word overlap algorithm
        if (algorithms.wordOverlap) {
            scores.wordOverlap = this.wordOverlapMatch(intendedWords, existingWords);
            weightedScore += scores.wordOverlap * weights.wordOverlap;
            totalWeight += weights.wordOverlap;
        }
        
        // Substring match algorithm
        if (algorithms.substring) {
            scores.substring = this.substringMatch(intended, existing);
            weightedScore += scores.substring * weights.substring;
            totalWeight += weights.substring;
        }
        
        // Partial match algorithm
        if (algorithms.partial) {
            scores.partial = this.partialMatch(intendedWords, existingWords);
            weightedScore += scores.partial * weights.partial;
            totalWeight += weights.partial;
        }
        
        const finalScore = totalWeight > 0 ? (weightedScore / totalWeight) : 0;
        const bestAlgorithm = Object.entries(scores).reduce((a, b) => scores[a[0]] > scores[b[0]] ? a : b)[0];
        
        return {
            score: Math.round(finalScore * 100) / 100,
            type: this.determineMatchType(scores, config),
            details: this.generateMatchDetails(scores, bestAlgorithm),
            algorithms: scores
        };
    }
    
    /**
     * Exact match algorithm (case-insensitive)
     */
    exactMatch(intended, existing) {
        return intended === existing ? 100 : 0;
    }
    
    /**
     * Levenshtein distance algorithm
     */
    levenshteinMatch(intended, existing) {
        const distance = this.levenshteinDistance(intended, existing);
        const maxLength = Math.max(intended.length, existing.length);
        const similarity = maxLength === 0 ? 100 : ((maxLength - distance) / maxLength) * 100;
        return Math.max(0, similarity);
    }
    
    /**
     * Word overlap algorithm
     */
    wordOverlapMatch(intendedWords, existingWords) {
        if (intendedWords.length === 0 || existingWords.length === 0) return 0;
        
        const intendedSet = new Set(intendedWords);
        const existingSet = new Set(existingWords);
        const intersection = new Set([...intendedSet].filter(x => existingSet.has(x)));
        const union = new Set([...intendedSet, ...existingSet]);
        
        return union.size === 0 ? 0 : (intersection.size / union.size) * 100;
    }
    
    /**
     * Substring match algorithm
     */
    substringMatch(intended, existing) {
        if (intended.length === 0 || existing.length === 0) return 0;
        
        const longerString = intended.length > existing.length ? intended : existing;
        const shorterString = intended.length > existing.length ? existing : intended;
        
        if (longerString.includes(shorterString)) {
            return (shorterString.length / longerString.length) * 100;
        }
        
        return 0;
    }
    
    /**
     * Partial match algorithm (word contains or is contained)
     */
    partialMatch(intendedWords, existingWords) {
        if (intendedWords.length === 0 || existingWords.length === 0) return 0;
        
        let matches = 0;
        let totalComparisons = 0;
        
        for (const intendedWord of intendedWords) {
            for (const existingWord of existingWords) {
                totalComparisons++;
                if (intendedWord.includes(existingWord) || existingWord.includes(intendedWord)) {
                    const similarity = Math.min(intendedWord.length, existingWord.length) / 
                                     Math.max(intendedWord.length, existingWord.length);
                    matches += similarity;
                }
            }
        }
        
        return totalComparisons === 0 ? 0 : (matches / totalComparisons) * 100;
    }
    
    /**
     * Calculate Levenshtein distance between two strings
     */
    levenshteinDistance(str1, str2) {
        const matrix = Array(str2.length + 1).fill(null).map(() => Array(str1.length + 1).fill(null));
        
        for (let i = 0; i <= str1.length; i++) matrix[0][i] = i;
        for (let j = 0; j <= str2.length; j++) matrix[j][0] = j;
        
        for (let j = 1; j <= str2.length; j++) {
            for (let i = 1; i <= str1.length; i++) {
                const indicator = str1[i - 1] === str2[j - 1] ? 0 : 1;
                matrix[j][i] = Math.min(
                    matrix[j][i - 1] + 1,
                    matrix[j - 1][i] + 1,
                    matrix[j - 1][i - 1] + indicator
                );
            }
        }
        
        return matrix[str2.length][str1.length];
    }
    
    /**
     * Normalize role name for comparison
     */
    normalizeRoleName(roleName) {
        return roleName.toLowerCase().trim();
    }
    
    /**
     * Extract words from role name
     */
    extractWords(roleName) {
        return roleName
            .split(/[\s\-_\.]+/)
            .filter(word => word.length > 0)
            .map(word => word.trim());
    }
    
    /**
     * Filter roles based on configuration
     */
    filterRoles(existingRoles, config) {
        const filtered = new Map();
        const now = Date.now();
        const minAgeMs = config.minRoleAgeHours * 60 * 60 * 1000;
        
        for (const [id, role] of existingRoles) {
            // Skip @everyone
            if (role.name === '@everyone') continue;
            
            // Skip system roles if configured
            if (config.excludeSystemRoles && role.managed) continue;
            
            // Skip bot roles if configured
            if (config.excludeBotRoles && role.tags?.botId) continue;
            
            // Skip integration roles if configured
            if (config.excludeIntegrationRoles && role.tags?.integrationId) continue;
            
            // Skip very new roles if configured
            if (config.minRoleAgeHours > 0 && (now - role.createdTimestamp) < minAgeMs) continue;
            
            filtered.set(id, role);
        }
        
        return filtered;
    }
    
    /**
     * Determine match type based on scores
     */
    determineMatchType(scores, config) {
        if (scores.exact >= config.exactMatchThreshold) return 'exact';
        if (scores.levenshtein >= config.partialMatchThreshold) return 'levenshtein';
        if (scores.wordOverlap >= config.partialMatchThreshold) return 'word_overlap';
        if (scores.substring >= config.substringMatchThreshold) return 'substring';
        if (scores.partial >= config.partialMatchThreshold) return 'partial';
        return 'fuzzy';
    }
    
    /**
     * Generate human-readable match details
     */
    generateMatchDetails(scores, bestAlgorithm) {
        const details = {
            exact: 'Exact name match',
            levenshtein: 'Similar spelling',
            wordOverlap: 'Common words',
            substring: 'Contains name',
            partial: 'Partial word match'
        };
        
        return details[bestAlgorithm] || 'Fuzzy match';
    }
    
    /**
     * Cache management methods
     */
    getFromCache(key, cacheMinutes) {
        const cached = this.matchCache.get(key);
        const timestamp = this.cacheTimestamps.get(key);
        
        if (cached && timestamp && (Date.now() - timestamp) < (cacheMinutes * 60 * 1000)) {
            return cached;
        }
        
        return null;
    }
    
    cacheResult(key, result) {
        this.matchCache.set(key, result);
        this.cacheTimestamps.set(key, Date.now());
    }
    
    clearCache() {
        this.matchCache.clear();
        this.cacheTimestamps.clear();
    }
}

module.exports = new EnhancedRoleMatchingEngine();
