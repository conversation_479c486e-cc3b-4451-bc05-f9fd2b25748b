{"scenario_1_initial_setup": {"description": "First time setup and deployment", "steps": ["Run /setup-application", "Add questions with verification settings", "Configure channels (application, logging, verification)", "Set up role assignments", "Enable system", "Deploy application panel", "Test application process"], "outcome": "✅ Fully functional application system"}, "scenario_2_update_questions": {"description": "Update questions and recreate panel", "steps": ["Modify existing questions", "Add new questions with verification", "Update role assignments", "Click 'Recreate Panel'", "Old panel replaced with updated info"], "outcome": "✅ Panel updated with new question info"}, "scenario_3_remove_system": {"description": "Temporarily remove application system", "steps": ["Click 'Remove Panel'", "Panel deleted from channel", "System remains configured", "Can redeploy later with same settings"], "outcome": "✅ Panel removed, config preserved"}, "scenario_4_complete_reset": {"description": "Complete system reset", "steps": ["Click 'Clear All Data'", "Confirm deletion", "All questions, configs, and submissions deleted", "Panel removed from channel", "System reset to default state"], "outcome": "✅ Complete clean slate for reconfiguration"}}