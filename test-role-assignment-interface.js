/**
 * Test script to verify the role assignment interface works correctly
 * This tests that the interface shows all question types and doesn't show outdated error messages
 */

// Test configuration with mixed question types
const testConfig = {
  applicationTypes: {
    'test_app': {
      name: 'Test Application',
      questions: [
        {
          question: 'What is your preferred role?',
          type: 'choice',
          options: ['Moderator', 'Helper', 'Member']
        },
        {
          question: 'What is your experience level?',
          type: 'text'
        },
        {
          question: 'Any additional comments?',
          type: 'text'
        }
      ],
      roleAssignments: {
        '0': {
          'Moderator': '123456789012345678',
          'Helper': '123456789012345679'
        },
        '1': {
          '__text_answer__': '123456789012345679'
        }
        // Question 2 has no role assignment
      }
    },
    'text_only_app': {
      name: 'Text Only Application',
      questions: [
        {
          question: 'Tell us about yourself',
          type: 'text'
        },
        {
          question: 'What are your goals?',
          type: 'text'
        }
      ],
      roleAssignments: {
        '0': {
          '__text_answer__': '123456789012345680'
        }
      }
    }
  }
};

// Test the role assignment info generation
function testRoleAssignmentInfo(appType) {
  console.log(`\n🧪 Testing role assignment info for: ${appType.name}`);
  
  const roleAssignmentInfo = appType.questions.map((q, i) => {
    const assignments = appType.roleAssignments?.[i] || {};
    const assignmentCount = Object.keys(assignments).length;
    const questionType = q.type === 'choice' ? 'Choice' : 'Text';
    const assignmentType = q.type === 'choice' ? 
      `${assignmentCount} option mappings` : 
      (assignmentCount > 0 ? '1 role assigned' : 'No role assigned');
    
    return `**${i + 1}.** ${q.question} *(${questionType})*\n└ ${assignmentType}`;
  }).join('\n\n');

  console.log('Generated role assignment info:');
  console.log(roleAssignmentInfo);
  
  // Verify no outdated messages
  const hasOutdatedMessages = roleAssignmentInfo.includes('choice questions only') || 
                             roleAssignmentInfo.includes('No Choice Questions');
  
  if (hasOutdatedMessages) {
    console.log('❌ FAIL: Contains outdated messages');
    return false;
  }
  
  // Verify all questions are shown
  const questionCount = appType.questions.length;
  const shownQuestions = (roleAssignmentInfo.match(/\*\*\d+\.\*\*/g) || []).length;
  
  if (shownQuestions !== questionCount) {
    console.log(`❌ FAIL: Expected ${questionCount} questions, but ${shownQuestions} shown`);
    return false;
  }
  
  // Verify question types are labeled
  const hasChoiceLabel = roleAssignmentInfo.includes('*(Choice)*');
  const hasTextLabel = roleAssignmentInfo.includes('*(Text)*');
  const hasChoiceQuestions = appType.questions.some(q => q.type === 'choice');
  const hasTextQuestions = appType.questions.some(q => q.type === 'text');
  
  if (hasChoiceQuestions && !hasChoiceLabel) {
    console.log('❌ FAIL: Choice questions not properly labeled');
    return false;
  }
  
  if (hasTextQuestions && !hasTextLabel) {
    console.log('❌ FAIL: Text questions not properly labeled');
    return false;
  }
  
  console.log('✅ PASS: Role assignment info generated correctly');
  return true;
}

// Test select menu options generation
function testSelectMenuOptions(appType) {
  console.log(`\n🧪 Testing select menu options for: ${appType.name}`);
  
  const options = appType.questions.map((q, i) => {
    const questionType = q.type === 'choice' ? 'Choice' : 'Text';
    const description = q.type === 'choice' ? 
      'Configure role mappings for each option' : 
      'Configure single role assignment';
    
    return {
      label: `Question ${i + 1}: ${q.question.substring(0, 45)}${q.question.length > 45 ? '...' : ''}`,
      value: i.toString(),
      description: description,
      emoji: q.type === 'choice' ? '🎭' : '📝'
    };
  });

  console.log('Generated select menu options:');
  options.forEach(option => {
    console.log(`  ${option.emoji} ${option.label}`);
    console.log(`    Description: ${option.description}`);
  });
  
  // Verify all questions have options
  if (options.length !== appType.questions.length) {
    console.log(`❌ FAIL: Expected ${appType.questions.length} options, got ${options.length}`);
    return false;
  }
  
  // Verify correct emojis and descriptions
  for (let i = 0; i < options.length; i++) {
    const question = appType.questions[i];
    const option = options[i];
    
    if (question.type === 'choice') {
      if (option.emoji !== '🎭' || !option.description.includes('role mappings for each option')) {
        console.log(`❌ FAIL: Choice question ${i + 1} has incorrect emoji or description`);
        return false;
      }
    } else if (question.type === 'text') {
      if (option.emoji !== '📝' || !option.description.includes('single role')) {
        console.log(`❌ FAIL: Text question ${i + 1} has incorrect emoji or description`);
        return false;
      }
    }
  }
  
  console.log('✅ PASS: Select menu options generated correctly');
  return true;
}

// Run tests
async function runTests() {
  console.log('🚀 Starting role assignment interface tests...\n');
  
  let passedTests = 0;
  let totalTests = 0;
  
  // Test mixed application (choice + text questions)
  totalTests++;
  if (testRoleAssignmentInfo(testConfig.applicationTypes.test_app)) {
    passedTests++;
  }
  
  totalTests++;
  if (testSelectMenuOptions(testConfig.applicationTypes.test_app)) {
    passedTests++;
  }
  
  // Test text-only application
  totalTests++;
  if (testRoleAssignmentInfo(testConfig.applicationTypes.text_only_app)) {
    passedTests++;
  }
  
  totalTests++;
  if (testSelectMenuOptions(testConfig.applicationTypes.text_only_app)) {
    passedTests++;
  }
  
  // Summary
  console.log('\n📊 Test Summary:');
  console.log(`✅ Passed: ${passedTests}/${totalTests}`);
  console.log(`❌ Failed: ${totalTests - passedTests}/${totalTests}`);
  
  if (passedTests === totalTests) {
    console.log('\n🎉 All tests passed! Role assignment interface is working correctly.');
    console.log('✅ No outdated "choice questions only" messages');
    console.log('✅ All question types are shown and properly labeled');
    console.log('✅ Correct UI elements for each question type');
  } else {
    console.log('\n⚠️ Some tests failed. Please review the implementation.');
  }
  
  return passedTests === totalTests;
}

// Run the tests
if (require.main === module) {
  runTests().catch(console.error);
}

module.exports = { runTests, testRoleAssignmentInfo, testSelectMenuOptions };
