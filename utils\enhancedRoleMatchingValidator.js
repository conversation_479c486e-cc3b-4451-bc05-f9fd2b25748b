/**
 * Enhanced Role Matching Validator
 * Provides comprehensive testing and validation for the enhanced role matching system
 */

const enhancedRoleMatchingEngine = require('./enhancedRoleMatchingEngine');
const enhancedAdminInterface = require('./enhancedAdminInterface');
const smartRoleAssignmentSystem = require('./smartRoleAssignmentSystem');
const roleMatchingConfig = require('./roleMatchingConfig');

class EnhancedRoleMatchingValidator {
    constructor() {
        this.testResults = [];
        this.performanceMetrics = [];
    }
    
    /**
     * Run comprehensive validation tests
     * @param {Object} guild - Discord guild object for testing
     * @returns {Object} Validation results
     */
    async runValidationTests(guild) {
        console.log('[VALIDATOR] Starting comprehensive validation tests...');
        
        const results = {
            timestamp: new Date().toISOString(),
            guildId: guild.id,
            guildName: guild.name,
            tests: [],
            summary: {
                total: 0,
                passed: 0,
                failed: 0,
                warnings: 0
            },
            performance: {},
            recommendations: []
        };
        
        try {
            // Test 1: Configuration System
            results.tests.push(await this.testConfigurationSystem(guild.id));
            
            // Test 2: Role Matching Engine
            results.tests.push(await this.testRoleMatchingEngine(guild));
            
            // Test 3: Admin Interface
            results.tests.push(await this.testAdminInterface());
            
            // Test 4: Role Assignment System
            results.tests.push(await this.testRoleAssignmentSystem(guild));
            
            // Test 5: Performance Tests
            results.tests.push(await this.testPerformance(guild));
            
            // Test 6: Edge Cases
            results.tests.push(await this.testEdgeCases(guild));
            
            // Test 7: Integration Tests
            results.tests.push(await this.testIntegration(guild));
            
            // Calculate summary
            results.summary.total = results.tests.length;
            results.summary.passed = results.tests.filter(t => t.status === 'passed').length;
            results.summary.failed = results.tests.filter(t => t.status === 'failed').length;
            results.summary.warnings = results.tests.filter(t => t.warnings && t.warnings.length > 0).length;
            
            // Generate recommendations
            results.recommendations = this.generateRecommendations(results.tests);
            
            console.log(`[VALIDATOR] Validation completed: ${results.summary.passed}/${results.summary.total} tests passed`);
            
        } catch (error) {
            console.error('[VALIDATOR] Error during validation:', error);
            results.error = error.message;
        }
        
        return results;
    }
    
    /**
     * Test configuration system
     */
    async testConfigurationSystem(guildId) {
        const test = {
            name: 'Configuration System',
            status: 'running',
            details: [],
            warnings: [],
            errors: []
        };
        
        try {
            // Test default configuration
            const defaultConfig = roleMatchingConfig.getConfig(guildId);
            if (!defaultConfig || typeof defaultConfig !== 'object') {
                throw new Error('Failed to get default configuration');
            }
            test.details.push('✓ Default configuration loaded');
            
            // Test configuration validation
            try {
                roleMatchingConfig.updateConfig(guildId, { similarityThreshold: 150 });
                test.errors.push('Configuration validation failed - invalid threshold accepted');
            } catch (error) {
                test.details.push('✓ Configuration validation working');
            }
            
            // Test preset application
            roleMatchingConfig.applyPreset(guildId, 'strict');
            const strictConfig = roleMatchingConfig.getConfig(guildId);
            if (strictConfig.similarityThreshold !== 85) {
                throw new Error('Preset application failed');
            }
            test.details.push('✓ Preset application working');
            
            // Reset to default
            roleMatchingConfig.resetConfig(guildId);
            test.details.push('✓ Configuration reset working');
            
            test.status = 'passed';
            
        } catch (error) {
            test.status = 'failed';
            test.errors.push(error.message);
        }
        
        return test;
    }
    
    /**
     * Test role matching engine
     */
    async testRoleMatchingEngine(guild) {
        const test = {
            name: 'Role Matching Engine',
            status: 'running',
            details: [],
            warnings: [],
            errors: []
        };
        
        try {
            const testCases = [
                { intended: 'level', expected: 'exact match for existing level role' },
                { intended: 'team-alpha', expected: 'partial match for alpha-team' },
                { intended: 'dev', expected: 'substring match for developers' },
                { intended: 'uniquerolename123', expected: 'no matches' },
                { intended: 'Level', expected: 'case insensitive exact match' }
            ];
            
            for (const testCase of testCases) {
                const startTime = Date.now();
                const matches = enhancedRoleMatchingEngine.findMatches(
                    testCase.intended,
                    guild.roles.cache,
                    guild.id
                );
                const processingTime = Date.now() - startTime;
                
                test.details.push(`✓ Processed "${testCase.intended}" in ${processingTime}ms (${matches.length} matches)`);
                
                if (processingTime > 2000) {
                    test.warnings.push(`Slow processing for "${testCase.intended}": ${processingTime}ms`);
                }
            }
            
            // Test algorithm weights
            const config = roleMatchingConfig.getConfig(guild.id);
            if (!config.algorithmWeights || Object.keys(config.algorithmWeights).length === 0) {
                test.warnings.push('No algorithm weights configured');
            }
            
            test.status = 'passed';
            
        } catch (error) {
            test.status = 'failed';
            test.errors.push(error.message);
        }
        
        return test;
    }
    
    /**
     * Test admin interface
     */
    async testAdminInterface() {
        const test = {
            name: 'Admin Interface',
            status: 'running',
            details: [],
            warnings: [],
            errors: []
        };
        
        try {
            // Test session management
            const sessionId = enhancedAdminInterface.generateSessionId('testUser', 'testGuild');
            const sessionData = {
                userId: 'testUser',
                guildId: 'testGuild',
                intendedRoleName: 'test',
                matches: []
            };
            
            enhancedAdminInterface.storeSession(sessionId, sessionData);
            const retrieved = enhancedAdminInterface.getSession(sessionId);
            
            if (!retrieved || retrieved.userId !== 'testUser') {
                throw new Error('Session storage/retrieval failed');
            }
            test.details.push('✓ Session management working');
            
            // Test interface creation
            const mockMatches = [{
                role: { name: 'test-role', id: '123', members: { size: 5 }, createdTimestamp: Date.now() },
                score: 85,
                matchType: 'exact',
                details: 'Test match'
            }];
            
            const { embed, components } = enhancedAdminInterface.createRoleSelectionInterface(
                'test',
                'test-channel',
                mockMatches,
                sessionId,
                roleMatchingConfig.getConfig('testGuild')
            );
            
            if (!embed || !components) {
                throw new Error('Interface creation failed');
            }
            test.details.push('✓ Interface creation working');
            
            // Cleanup
            enhancedAdminInterface.deleteSession(sessionId);
            test.details.push('✓ Session cleanup working');
            
            test.status = 'passed';
            
        } catch (error) {
            test.status = 'failed';
            test.errors.push(error.message);
        }
        
        return test;
    }
    
    /**
     * Test role assignment system
     */
    async testRoleAssignmentSystem(guild) {
        const test = {
            name: 'Role Assignment System',
            status: 'running',
            details: [],
            warnings: [],
            errors: []
        };
        
        try {
            // Test permission calculation
            const mockRole = { id: '123', name: 'test-role' };
            const permissions = await smartRoleAssignmentSystem.calculateChannelPermissions(
                guild,
                mockRole,
                'text',
                null,
                null
            );
            
            if (!permissions || permissions.length === 0) {
                throw new Error('Permission calculation failed');
            }
            test.details.push('✓ Permission calculation working');
            
            // Test input validation
            try {
                smartRoleAssignmentSystem.validateInputs({
                    guild: null,
                    channelName: 'test',
                    role: mockRole
                });
                test.errors.push('Input validation failed - null guild accepted');
            } catch (error) {
                test.details.push('✓ Input validation working');
            }
            
            // Test audit logging
            const initialLogLength = smartRoleAssignmentSystem.getAuditLog(1).length;
            smartRoleAssignmentSystem.logAssignment({
                type: 'test_log',
                timestamp: Date.now()
            });
            const newLogLength = smartRoleAssignmentSystem.getAuditLog(1).length;
            
            if (newLogLength <= initialLogLength) {
                test.warnings.push('Audit logging may not be working correctly');
            } else {
                test.details.push('✓ Audit logging working');
            }
            
            test.status = 'passed';
            
        } catch (error) {
            test.status = 'failed';
            test.errors.push(error.message);
        }
        
        return test;
    }
    
    /**
     * Test performance
     */
    async testPerformance(guild) {
        const test = {
            name: 'Performance Tests',
            status: 'running',
            details: [],
            warnings: [],
            errors: []
        };
        
        try {
            const testRoles = ['test1', 'test2', 'test3', 'test4', 'test5'];
            const times = [];
            
            for (const roleName of testRoles) {
                const startTime = Date.now();
                enhancedRoleMatchingEngine.findMatches(roleName, guild.roles.cache, guild.id);
                const endTime = Date.now();
                times.push(endTime - startTime);
            }
            
            const avgTime = times.reduce((a, b) => a + b, 0) / times.length;
            const maxTime = Math.max(...times);
            
            test.details.push(`✓ Average processing time: ${avgTime.toFixed(2)}ms`);
            test.details.push(`✓ Maximum processing time: ${maxTime}ms`);
            
            if (avgTime > 500) {
                test.warnings.push(`Average processing time is high: ${avgTime.toFixed(2)}ms`);
            }
            
            if (maxTime > 2000) {
                test.warnings.push(`Maximum processing time exceeds threshold: ${maxTime}ms`);
            }
            
            // Test memory usage (basic check)
            const memUsage = process.memoryUsage();
            test.details.push(`✓ Memory usage: ${(memUsage.heapUsed / 1024 / 1024).toFixed(2)}MB`);
            
            test.status = 'passed';
            
        } catch (error) {
            test.status = 'failed';
            test.errors.push(error.message);
        }
        
        return test;
    }
    
    /**
     * Test edge cases
     */
    async testEdgeCases(guild) {
        const test = {
            name: 'Edge Cases',
            status: 'running',
            details: [],
            warnings: [],
            errors: []
        };
        
        try {
            const edgeCases = [
                '', // Empty string
                '   ', // Whitespace only
                'a'.repeat(101), // Too long
                '!@#$%^&*()', // Special characters
                'роль', // Unicode characters
                'role with multiple    spaces'
            ];
            
            for (const edgeCase of edgeCases) {
                try {
                    const matches = enhancedRoleMatchingEngine.findMatches(
                        edgeCase,
                        guild.roles.cache,
                        guild.id
                    );
                    test.details.push(`✓ Handled edge case: "${edgeCase}" (${matches.length} matches)`);
                } catch (error) {
                    test.warnings.push(`Edge case failed: "${edgeCase}" - ${error.message}`);
                }
            }
            
            // Test with empty role cache
            const emptyCache = new Map();
            const matches = enhancedRoleMatchingEngine.findMatches('test', emptyCache, guild.id);
            if (matches.length !== 0) {
                test.warnings.push('Empty role cache should return no matches');
            } else {
                test.details.push('✓ Empty role cache handled correctly');
            }
            
            test.status = 'passed';
            
        } catch (error) {
            test.status = 'failed';
            test.errors.push(error.message);
        }
        
        return test;
    }
    
    /**
     * Test integration
     */
    async testIntegration(guild) {
        const test = {
            name: 'Integration Tests',
            status: 'running',
            details: [],
            warnings: [],
            errors: []
        };
        
        try {
            // Test full workflow simulation (without actual Discord API calls)
            const mockContext = {
                guild: guild,
                guildId: guild.id,
                userId: 'testUser',
                channelName: 'test-channel',
                channelType: 'text',
                roleOptions: {},
                operationType: 'private_channel_creation'
            };
            
            // This would normally call the full workflow, but we'll just test the matching part
            const matches = enhancedRoleMatchingEngine.findMatches(
                'test-integration',
                guild.roles.cache,
                guild.id
            );
            
            test.details.push(`✓ Integration test completed (${matches.length} matches found)`);
            
            // Test configuration integration
            const config = roleMatchingConfig.getConfig(guild.id);
            if (matches.length <= config.maxMatchesToDisplay) {
                test.details.push('✓ Match limit configuration respected');
            } else {
                test.warnings.push('Match limit configuration not respected');
            }
            
            test.status = 'passed';
            
        } catch (error) {
            test.status = 'failed';
            test.errors.push(error.message);
        }
        
        return test;
    }
    
    /**
     * Generate recommendations based on test results
     */
    generateRecommendations(tests) {
        const recommendations = [];
        
        // Performance recommendations
        const perfTest = tests.find(t => t.name === 'Performance Tests');
        if (perfTest && perfTest.warnings.length > 0) {
            recommendations.push({
                type: 'performance',
                priority: 'medium',
                message: 'Consider optimizing role matching algorithms or reducing the number of roles to scan',
                details: perfTest.warnings
            });
        }
        
        // Configuration recommendations
        const configTest = tests.find(t => t.name === 'Configuration System');
        if (configTest && configTest.status === 'passed') {
            recommendations.push({
                type: 'configuration',
                priority: 'low',
                message: 'Consider customizing similarity thresholds based on your server\'s role naming patterns',
                details: ['Use /config-role-matching command to adjust settings']
            });
        }
        
        // Edge case recommendations
        const edgeTest = tests.find(t => t.name === 'Edge Cases');
        if (edgeTest && edgeTest.warnings.length > 0) {
            recommendations.push({
                type: 'robustness',
                priority: 'high',
                message: 'Some edge cases are not handled properly',
                details: edgeTest.warnings
            });
        }
        
        return recommendations;
    }
    
    /**
     * Generate validation report
     */
    generateReport(validationResults) {
        let report = `# Enhanced Role Matching System Validation Report\n\n`;
        report += `**Generated:** ${validationResults.timestamp}\n`;
        report += `**Guild:** ${validationResults.guildName} (${validationResults.guildId})\n\n`;
        
        report += `## Summary\n`;
        report += `- **Total Tests:** ${validationResults.summary.total}\n`;
        report += `- **Passed:** ${validationResults.summary.passed}\n`;
        report += `- **Failed:** ${validationResults.summary.failed}\n`;
        report += `- **Warnings:** ${validationResults.summary.warnings}\n\n`;
        
        report += `## Test Results\n\n`;
        validationResults.tests.forEach(test => {
            const status = test.status === 'passed' ? '✅' : '❌';
            report += `### ${status} ${test.name}\n`;
            
            if (test.details.length > 0) {
                report += `**Details:**\n`;
                test.details.forEach(detail => report += `- ${detail}\n`);
            }
            
            if (test.warnings.length > 0) {
                report += `**Warnings:**\n`;
                test.warnings.forEach(warning => report += `- ⚠️ ${warning}\n`);
            }
            
            if (test.errors.length > 0) {
                report += `**Errors:**\n`;
                test.errors.forEach(error => report += `- ❌ ${error}\n`);
            }
            
            report += '\n';
        });
        
        if (validationResults.recommendations.length > 0) {
            report += `## Recommendations\n\n`;
            validationResults.recommendations.forEach(rec => {
                const priority = rec.priority === 'high' ? '🔴' : rec.priority === 'medium' ? '🟡' : '🟢';
                report += `### ${priority} ${rec.type.toUpperCase()}\n`;
                report += `${rec.message}\n\n`;
                if (rec.details.length > 0) {
                    rec.details.forEach(detail => report += `- ${detail}\n`);
                    report += '\n';
                }
            });
        }
        
        return report;
    }
}

module.exports = new EnhancedRoleMatchingValidator();
