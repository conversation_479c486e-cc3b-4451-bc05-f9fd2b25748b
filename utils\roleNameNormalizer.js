/**
 * Role Name Normalizer
 * Intelligent normalization of role names for fuzzy matching
 * Focuses on semantic content while ignoring formatting differences
 */

class RoleNameNormalizer {
    constructor() {
        // Characters to remove completely
        this.removeChars = [
            '"', "'", '`',           // Quotation marks
            '—', '–', '-',           // Various dashes and hyphens
            '/', '\\',               // Slashes
            '(', ')', '[', ']',      // Brackets
            '{', '}',                // Braces
            '|', '~', '^',           // Special symbols
            '*', '+', '=',           // Math symbols
            '<', '>',                // Angle brackets
            '!', '?',                // Punctuation (except periods and commas in some contexts)
            '#', '$', '%', '&',      // Special characters
            '@'                      // At symbol
        ];
        
        // Characters to replace with spaces
        this.spaceChars = [
            '_', '.', ',', ';', ':' // Underscores, periods, commas, semicolons, colons
        ];
        
        // Common word separators that should become spaces
        this.separatorPatterns = [
            /\s*-\s*/g,              // Hyphens with optional spaces
            /\s*\/\s*/g,             // Slashes with optional spaces
            /\s*\|\s*/g,             // Pipes with optional spaces
            /\s*_\s*/g,              // Underscores with optional spaces
            /\s*\.\s*/g,             // Periods with optional spaces (but preserve in numbers)
            /\s*,\s*/g,              // Commas with optional spaces
            /\s*;\s*/g,              // Semicolons with optional spaces
            /\s*:\s*/g               // Colons with optional spaces
        ];
    }

    /**
     * Normalize a role name for comparison
     * @param {string} roleName - The original role name
     * @returns {string} Normalized role name
     */
    normalize(roleName) {
        if (!roleName || typeof roleName !== 'string') {
            return '';
        }

        let normalized = roleName;

        // Step 1: Convert to lowercase for case-insensitive comparison
        normalized = normalized.toLowerCase();

        // Step 2: Handle special number patterns (preserve decimal points in numbers)
        normalized = this.preserveNumberFormats(normalized);

        // Step 3: Replace separator patterns with single spaces
        this.separatorPatterns.forEach(pattern => {
            normalized = normalized.replace(pattern, ' ');
        });

        // Step 4: Remove unwanted characters
        this.removeChars.forEach(char => {
            normalized = normalized.replace(new RegExp('\\' + char, 'g'), '');
        });

        // Step 5: Replace space characters with actual spaces
        this.spaceChars.forEach(char => {
            normalized = normalized.replace(new RegExp('\\' + char, 'g'), ' ');
        });

        // Step 6: Normalize whitespace
        normalized = this.normalizeWhitespace(normalized);

        // Step 7: Handle common abbreviations and variations
        normalized = this.normalizeCommonVariations(normalized);

        return normalized;
    }

    /**
     * Preserve number formats during normalization
     * @param {string} text - Text to process
     * @returns {string} Text with preserved number formats
     */
    preserveNumberFormats(text) {
        // Preserve decimal numbers (e.g., "level 5.5" should stay "level 5.5")
        // Preserve version numbers (e.g., "v1.2.3" should stay "v1 2 3")
        
        // Handle version numbers - replace dots with spaces in version patterns
        text = text.replace(/v(\d+)\.(\d+)\.?(\d+)?/g, (match, major, minor, patch) => {
            return patch ? `v${major} ${minor} ${patch}` : `v${major} ${minor}`;
        });
        
        // Handle decimal numbers - keep them as is for now
        // We'll be more careful about dots in numbers vs separators
        
        return text;
    }

    /**
     * Normalize whitespace
     * @param {string} text - Text to normalize
     * @returns {string} Text with normalized whitespace
     */
    normalizeWhitespace(text) {
        return text
            .replace(/\s+/g, ' ')    // Multiple spaces become single space
            .trim();                 // Remove leading/trailing spaces
    }

    /**
     * Normalize common variations and abbreviations
     * @param {string} text - Text to normalize
     * @returns {string} Text with normalized variations
     */
    normalizeCommonVariations(text) {
        // Common abbreviations and variations
        const variations = {
            'admin': ['administrator', 'admins', 'administrators'],
            'mod': ['moderator', 'mods', 'moderators'],
            'dev': ['developer', 'devs', 'developers'],
            'member': ['members', 'user', 'users'],
            'staff': ['staffs'],
            'vip': ['v i p'],
            'level': ['lvl', 'lv'],
            'tier': ['tiers'],
            'rank': ['ranks'],
            'team': ['teams', 'group', 'groups']
        };

        // Replace variations with standard forms
        Object.entries(variations).forEach(([standard, variants]) => {
            variants.forEach(variant => {
                const regex = new RegExp(`\\b${variant}\\b`, 'g');
                text = text.replace(regex, standard);
            });
        });

        return text;
    }

    /**
     * Extract core alphanumeric content
     * @param {string} text - Text to process
     * @returns {string} Core alphanumeric content
     */
    extractCore(text) {
        // Remove all non-alphanumeric characters except spaces
        return text.replace(/[^a-z0-9\s]/g, '').replace(/\s+/g, ' ').trim();
    }

    /**
     * Get multiple normalized versions for comprehensive matching
     * @param {string} roleName - Original role name
     * @returns {Object} Object with different normalized versions
     */
    getNormalizedVersions(roleName) {
        const basic = this.normalize(roleName);
        const core = this.extractCore(basic);
        const compact = basic.replace(/\s/g, ''); // No spaces version
        const words = basic.split(' ').filter(word => word.length > 0);

        return {
            original: roleName,
            basic: basic,           // Standard normalization
            core: core,             // Only alphanumeric + spaces
            compact: compact,       // No spaces
            words: words,           // Array of individual words
            wordCount: words.length
        };
    }

    /**
     * Calculate similarity between two normalized role names
     * @param {string} name1 - First role name
     * @param {string} name2 - Second role name
     * @returns {Object} Similarity analysis
     */
    calculateSimilarity(name1, name2) {
        const norm1 = this.getNormalizedVersions(name1);
        const norm2 = this.getNormalizedVersions(name2);

        // Exact match on any normalized version
        if (norm1.basic === norm2.basic || 
            norm1.core === norm2.core || 
            norm1.compact === norm2.compact) {
            return {
                score: 100,
                matchType: 'exact',
                reason: 'Exact match after normalization',
                normalized1: norm1.basic,
                normalized2: norm2.basic
            };
        }

        // Word-based matching
        const wordSimilarity = this.calculateWordSimilarity(norm1.words, norm2.words);
        
        // Levenshtein distance on normalized versions
        const levenshteinScore = this.calculateLevenshteinSimilarity(norm1.basic, norm2.basic);
        
        // Enhanced substring matching (prioritized for channel name detection)
        const substringScore = this.calculateSubstringSimilarity(norm1.basic, norm2.basic);

        // Enhanced channel name matching (check if name1 appears in name2 as intended channel name)
        const channelNameScore = this.calculateChannelNameSubstringMatch(norm1.basic, norm2.basic);

        // Take the highest score, prioritizing channel name matches
        const maxScore = Math.max(wordSimilarity.score, levenshteinScore, substringScore, channelNameScore);

        let matchType = 'none';
        let reason = 'No significant similarity';

        // Enhanced scoring with better channel name detection
        if (maxScore >= 95) {
            matchType = 'near_exact';
            reason = channelNameScore >= 90 ? 'Channel name found in role name' : 'Very high similarity after normalization';
        } else if (maxScore >= 85) {
            matchType = 'high';
            reason = channelNameScore >= 85 ? 'Channel name matches role pattern' : 'High similarity';
        } else if (maxScore >= 75) {
            matchType = 'medium';
            reason = channelNameScore >= 75 ? 'Channel words found in role' : 'Medium similarity';
        } else if (maxScore >= 70) {
            matchType = 'medium';
            reason = 'Medium similarity';
        } else if (maxScore >= 50) {
            matchType = 'low';
            reason = 'Low similarity';
        }

        return {
            score: Math.round(maxScore),
            matchType: matchType,
            reason: reason,
            normalized1: norm1.basic,
            normalized2: norm2.basic,
            details: {
                wordSimilarity: wordSimilarity,
                levenshteinScore: levenshteinScore,
                substringScore: substringScore,
                channelNameScore: channelNameScore
            }
        };
    }

    /**
     * Calculate word-based similarity
     * @param {Array} words1 - First set of words
     * @param {Array} words2 - Second set of words
     * @returns {Object} Word similarity analysis
     */
    calculateWordSimilarity(words1, words2) {
        if (words1.length === 0 && words2.length === 0) {
            return { score: 100, matchedWords: [], reason: 'Both empty' };
        }
        
        if (words1.length === 0 || words2.length === 0) {
            return { score: 0, matchedWords: [], reason: 'One set empty' };
        }

        const matchedWords = [];
        const totalWords = Math.max(words1.length, words2.length);
        
        // Find exact word matches
        words1.forEach(word1 => {
            if (words2.includes(word1)) {
                matchedWords.push(word1);
            }
        });

        const exactMatches = matchedWords.length;
        const exactScore = (exactMatches / totalWords) * 100;

        // If all words match exactly, it's a perfect match
        if (exactMatches === words1.length && exactMatches === words2.length) {
            return { score: 100, matchedWords: matchedWords, reason: 'All words match exactly' };
        }

        return { 
            score: exactScore, 
            matchedWords: matchedWords, 
            reason: `${exactMatches}/${totalWords} words match` 
        };
    }

    /**
     * Calculate Levenshtein distance similarity
     * @param {string} str1 - First string
     * @param {string} str2 - Second string
     * @returns {number} Similarity percentage
     */
    calculateLevenshteinSimilarity(str1, str2) {
        const distance = this.levenshteinDistance(str1, str2);
        const maxLength = Math.max(str1.length, str2.length);
        
        if (maxLength === 0) return 100;
        
        return ((maxLength - distance) / maxLength) * 100;
    }

    /**
     * Calculate Levenshtein distance
     * @param {string} str1 - First string
     * @param {string} str2 - Second string
     * @returns {number} Edit distance
     */
    levenshteinDistance(str1, str2) {
        const matrix = [];
        
        for (let i = 0; i <= str2.length; i++) {
            matrix[i] = [i];
        }
        
        for (let j = 0; j <= str1.length; j++) {
            matrix[0][j] = j;
        }
        
        for (let i = 1; i <= str2.length; i++) {
            for (let j = 1; j <= str1.length; j++) {
                if (str2.charAt(i - 1) === str1.charAt(j - 1)) {
                    matrix[i][j] = matrix[i - 1][j - 1];
                } else {
                    matrix[i][j] = Math.min(
                        matrix[i - 1][j - 1] + 1,
                        matrix[i][j - 1] + 1,
                        matrix[i - 1][j] + 1
                    );
                }
            }
        }
        
        return matrix[str2.length][str1.length];
    }

    /**
     * Calculate substring similarity with enhanced channel name matching
     * @param {string} str1 - First string (intended channel name)
     * @param {string} str2 - Second string (existing role name)
     * @returns {number} Similarity percentage
     */
    calculateSubstringSimilarity(str1, str2) {
        if (str1 === str2) return 100;
        if (str1.length === 0 || str2.length === 0) return 0;

        // Enhanced substring matching for channel names
        const channelNameMatch = this.calculateChannelNameSubstringMatch(str1, str2);
        if (channelNameMatch > 0) {
            return channelNameMatch;
        }

        // Check if one is a substring of the other
        if (str1.includes(str2) || str2.includes(str1)) {
            const shorter = str1.length < str2.length ? str1 : str2;
            const longer = str1.length >= str2.length ? str1 : str2;
            return (shorter.length / longer.length) * 90; // 90% for substring matches
        }

        // Find longest common substring
        const lcs = this.longestCommonSubstring(str1, str2);
        const maxLength = Math.max(str1.length, str2.length);

        return (lcs.length / maxLength) * 80; // 80% max for partial substring matches
    }

    /**
     * Enhanced channel name substring matching
     * Specifically designed to detect cases like "Game 1" in "Game idp 1"
     * @param {string} channelName - Intended channel name
     * @param {string} roleName - Existing role name
     * @returns {number} Similarity percentage (0-95)
     */
    calculateChannelNameSubstringMatch(channelName, roleName) {
        // Split both names into words
        const channelWords = channelName.split(' ').filter(word => word.length > 0);
        const roleWords = roleName.split(' ').filter(word => word.length > 0);

        if (channelWords.length === 0 || roleWords.length === 0) return 0;

        // Check if the channel name appears as a complete substring in the role name
        if (roleName.includes(channelName)) {
            // High score for exact substring match
            const ratio = channelName.length / roleName.length;
            return Math.min(95, 85 + (ratio * 10)); // 85-95% based on how much of the role name is the channel name
        }

        // Check if all channel words appear in the role in the same order
        const orderedWordMatch = this.checkOrderedWordMatch(channelWords, roleWords);
        if (orderedWordMatch.isMatch) {
            // Very high score for ordered word matches (like "Game 1" in "Game idp 1")
            const coverage = orderedWordMatch.matchedWords / channelWords.length;
            const efficiency = orderedWordMatch.matchedWords / roleWords.length;
            return Math.min(92, 80 + (coverage * 10) + (efficiency * 2));
        }

        // Check if all channel words appear in the role (any order)
        const allWordsMatch = this.checkAllWordsMatch(channelWords, roleWords);
        if (allWordsMatch.isMatch) {
            const coverage = allWordsMatch.matchedWords / channelWords.length;
            const efficiency = allWordsMatch.matchedWords / roleWords.length;
            return Math.min(88, 75 + (coverage * 8) + (efficiency * 5));
        }

        return 0;
    }

    /**
     * Check if channel words appear in role words in the same order
     * @param {Array} channelWords - Words from channel name
     * @param {Array} roleWords - Words from role name
     * @returns {Object} Match result with details
     */
    checkOrderedWordMatch(channelWords, roleWords) {
        let channelIndex = 0;
        let matchedWords = 0;

        for (let roleIndex = 0; roleIndex < roleWords.length && channelIndex < channelWords.length; roleIndex++) {
            if (roleWords[roleIndex] === channelWords[channelIndex]) {
                matchedWords++;
                channelIndex++;
            }
        }

        return {
            isMatch: matchedWords === channelWords.length,
            matchedWords: matchedWords,
            totalChannelWords: channelWords.length,
            totalRoleWords: roleWords.length
        };
    }

    /**
     * Check if all channel words appear in role words (any order)
     * @param {Array} channelWords - Words from channel name
     * @param {Array} roleWords - Words from role name
     * @returns {Object} Match result with details
     */
    checkAllWordsMatch(channelWords, roleWords) {
        const roleWordSet = new Set(roleWords);
        let matchedWords = 0;

        for (const channelWord of channelWords) {
            if (roleWordSet.has(channelWord)) {
                matchedWords++;
            }
        }

        return {
            isMatch: matchedWords === channelWords.length,
            matchedWords: matchedWords,
            totalChannelWords: channelWords.length,
            totalRoleWords: roleWords.length
        };
    }

    /**
     * Find longest common substring
     * @param {string} str1 - First string
     * @param {string} str2 - Second string
     * @returns {string} Longest common substring
     */
    longestCommonSubstring(str1, str2) {
        let longest = '';
        
        for (let i = 0; i < str1.length; i++) {
            for (let j = i + 1; j <= str1.length; j++) {
                const substring = str1.slice(i, j);
                if (str2.includes(substring) && substring.length > longest.length) {
                    longest = substring;
                }
            }
        }
        
        return longest;
    }
}

module.exports = new RoleNameNormalizer();
