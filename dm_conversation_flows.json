{"scenario_dm_text_questions": {"description": "User with open DMs - Text questions", "conversation": ["Bot: 📋 Server Application Form - Welcome embed with Start Form button", "User: *clicks Start Form*", "Bot: 📝 Question 1 of 3 - What is your name? (embed with 'type your answer below')", "User: <PERSON>", "Bot: ✅ Answer Verified - Question 1 completed (verification result embed)", "Bot: 📝 Question 2 of 3 - What is your email? (embed with 'type your answer below')", "User: <EMAIL>", "Bot: ✅ Answer Verified - Question 2 completed (verification result embed)", "Bot: 📝 Question 3 of 3 - What is your phone? (embed with 'type your answer below')", "User: 555-1234", "Bot: ❌ Verification Failed - Answer not found in channels", "Bot: 🔄 Please Try Again - Type corrected answer below", "User: 555-5678", "Bot: ✅ Answer Verified - Question 3 completed", "Bot: ✅ Application Completed Successfully! - Roles assigned"]}, "scenario_dm_choice_questions": {"description": "User with open DMs - Choice questions", "conversation": ["Bot: 📋 Server Application Form - Welcome embed with Start Form button", "User: *clicks Start Form*", "Bot: 📝 Question 1 of 2 - What's your favorite language? (embed with numbered options)", "Bot: 1. JavaScript, 2. Python, 3. Java (in embed)", "User: 2", "Bot: ✅ Answer Verified - Python selected", "Bot: 📝 Question 2 of 2 - What's your experience level? (embed with options)", "Bot: 1. <PERSON><PERSON><PERSON>, 2. <PERSON>, 3. <PERSON>", "User: Intermediate", "Bot: ✅ Answer Verified - Intermediate selected", "Bot: ✅ Application Completed Successfully! - Roles assigned"]}, "scenario_dm_closed": {"description": "User with closed DMs - Modal fallback", "conversation": ["Bot: (in server) 📋 Server Application Form - DMs closed, using modal system", "User: *clicks Start Form*", "Bot: (modal) Question 1 of 3 - Text input modal", "User: *submits modal*", "Bot: (ephemeral) ✅ Answer Verified - Question 1 completed", "Bot: (modal) Question 2 of 3 - Text input modal", "User: *submits modal*", "Bot: (ephemeral) ✅ Answer Verified - Question 2 completed", "Bot: (ephemeral) ✅ Application Completed Successfully!"]}}