/**
 * Enhanced Batch Processor for Role Matching
 * Handles multiple role matching operations in a user-friendly way
 */

const { EmbedBuilder, ActionRowBuilder, ButtonBuilder, ButtonStyle, StringSelectMenuBuilder, StringSelectMenuOptionBuilder } = require('discord.js');

class EnhancedBatchProcessor {
    constructor() {
        this.batchSessions = new Map();
    }

    /**
     * Process multiple role matching operations in batch
     * @param {Array} operations - Array of role matching operations
     * @param {Object} context - Context object with guild, user info, etc.
     * @returns {Object} Batch processing result
     */
    async processBatch(operations, context) {
        const batchId = this.generateBatchId(context.userId, context.guildId);
        
        // Categorize operations
        const categorized = this.categorizeOperations(operations);
        
        // Store batch session
        this.batchSessions.set(batchId, {
            userId: context.userId,
            guildId: context.guildId,
            operations: operations,
            categorized: categorized,
            context: context,
            createdAt: Date.now(),
            currentStep: 0,
            completedOperations: [],
            pendingOperations: [...operations]
        });

        // Determine processing mode
        const config = context.config || {};
        const mode = config.batchProcessingMode || 'sequential';

        switch (mode) {
            case 'sequential':
                return await this.createSequentialInterface(batchId, categorized);
            case 'summary':
                return await this.createSummaryInterface(batchId, categorized);
            case 'grouped':
                return await this.createGroupedInterface(batchId, categorized);
            default:
                return await this.createSequentialInterface(batchId, categorized);
        }
    }

    /**
     * Categorize operations by their status
     */
    categorizeOperations(operations) {
        const categorized = {
            exactMatches: [],
            ambiguousMatches: [],
            noMatches: [],
            errors: []
        };

        operations.forEach((op, index) => {
            op.originalIndex = index;
            
            if (op.error) {
                categorized.errors.push(op);
            } else if (!op.matches || op.matches.length === 0) {
                categorized.noMatches.push(op);
            } else if (op.matches.length === 1 && op.matches[0].matchType === 'exact') {
                categorized.exactMatches.push(op);
            } else {
                categorized.ambiguousMatches.push(op);
            }
        });

        return categorized;
    }

    /**
     * Create sequential processing interface
     */
    async createSequentialInterface(batchId, categorized) {
        const session = this.batchSessions.get(batchId);
        
        // Start with the first ambiguous match that needs user input
        if (categorized.ambiguousMatches.length > 0) {
            const currentOp = categorized.ambiguousMatches[0];
            
            const embed = new EmbedBuilder()
                .setTitle('🎯 Role Matching - Sequential Processing')
                .setColor(0x3498db)
                .setDescription(
                    `**Processing:** ${currentOp.intendedRoleName}\n` +
                    `**Progress:** 1 of ${categorized.ambiguousMatches.length} requiring decisions\n\n` +
                    `**Summary:**\n` +
                    `✅ ${categorized.exactMatches.length} exact matches (auto-processed)\n` +
                    `🆕 ${categorized.noMatches.length} new roles (auto-created)\n` +
                    `❓ ${categorized.ambiguousMatches.length} need your decision\n` +
                    `❌ ${categorized.errors.length} errors\n\n` +
                    `**Found ${currentOp.matches.length} similar roles:**`
                )
                .setTimestamp();

            // Add match details
            currentOp.matches.slice(0, 3).forEach((match, index) => {
                embed.addFields({
                    name: `${index + 1}. ${match.role.name}`,
                    value: `${Math.round(match.score)}% match • ${match.role.members.size} members • ${match.matchType}`,
                    inline: true
                });
            });

            const components = this.createSequentialComponents(batchId, currentOp);
            
            return {
                success: true,
                requiresInteraction: true,
                batchId,
                embed,
                components,
                mode: 'sequential'
            };
        }

        // No ambiguous matches, return summary
        return this.createCompletionSummary(batchId, categorized);
    }

    /**
     * Create components for sequential processing
     */
    createSequentialComponents(batchId, operation) {
        const components = [];

        // Role selection dropdown if multiple matches
        if (operation.matches.length > 1) {
            const selectMenu = new StringSelectMenuBuilder()
                .setCustomId(`batch_role_select_${batchId}`)
                .setPlaceholder('🎯 Choose which role to use...')
                .setMinValues(1)
                .setMaxValues(1);

            operation.matches.slice(0, 3).forEach((match, index) => {
                selectMenu.addOptions(
                    new StringSelectMenuOptionBuilder()
                        .setLabel(match.role.name.length > 80 ? match.role.name.substring(0, 77) + '...' : match.role.name)
                        .setDescription(`${Math.round(match.score)}% match • ${match.role.members.size} members`)
                        .setValue(`${index}`)
                        .setEmoji(this.getMatchTypeEmoji(match.matchType))
                );
            });

            components.push(new ActionRowBuilder().addComponents(selectMenu));
        }

        // Action buttons
        const actionRow = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId(`batch_use_${batchId}`)
                    .setLabel(operation.matches.length > 1 ? 'Use Selected' : 'Use This Role')
                    .setStyle(ButtonStyle.Success)
                    .setEmoji('✅'),
                new ButtonBuilder()
                    .setCustomId(`batch_create_${batchId}`)
                    .setLabel('Create New')
                    .setStyle(ButtonStyle.Primary)
                    .setEmoji('➕'),
                new ButtonBuilder()
                    .setCustomId(`batch_skip_${batchId}`)
                    .setLabel('Skip This')
                    .setStyle(ButtonStyle.Secondary)
                    .setEmoji('⏭️')
            );

        components.push(actionRow);

        // Control buttons
        const controlRow = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId(`batch_auto_all_${batchId}`)
                    .setLabel('Auto-Process All')
                    .setStyle(ButtonStyle.Secondary)
                    .setEmoji('🤖'),
                new ButtonBuilder()
                    .setCustomId(`batch_cancel_${batchId}`)
                    .setLabel('Cancel Batch')
                    .setStyle(ButtonStyle.Danger)
                    .setEmoji('❌')
            );

        components.push(controlRow);

        return components;
    }

    /**
     * Create completion summary
     */
    createCompletionSummary(batchId, categorized) {
        const embed = new EmbedBuilder()
            .setTitle('✅ Batch Processing Complete')
            .setColor(0x00ff00)
            .setDescription(
                `**Processing Summary:**\n` +
                `✅ ${categorized.exactMatches.length} exact matches processed\n` +
                `🆕 ${categorized.noMatches.length} new roles created\n` +
                `❓ ${categorized.ambiguousMatches.length} decisions made\n` +
                `❌ ${categorized.errors.length} errors encountered`
            )
            .setTimestamp();

        return {
            success: true,
            requiresInteraction: false,
            embed,
            complete: true
        };
    }

    /**
     * Get emoji for match type
     */
    getMatchTypeEmoji(matchType) {
        const emojis = {
            exact: '🎯',
            levenshtein: '📝',
            word_overlap: '🔤',
            substring: '🔍',
            partial: '📄',
            fuzzy: '❓'
        };
        return emojis[matchType] || '❓';
    }

    /**
     * Generate batch ID
     */
    generateBatchId(userId, guildId) {
        return `batch_${userId}_${guildId}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }

    /**
     * Get batch session
     */
    getBatchSession(batchId) {
        return this.batchSessions.get(batchId);
    }

    /**
     * Update batch session
     */
    updateBatchSession(batchId, updates) {
        const session = this.batchSessions.get(batchId);
        if (session) {
            Object.assign(session, updates);
            this.batchSessions.set(batchId, session);
        }
    }

    /**
     * Clean up expired sessions
     */
    cleanupExpiredSessions() {
        const now = Date.now();
        const maxAge = 30 * 60 * 1000; // 30 minutes

        for (const [batchId, session] of this.batchSessions.entries()) {
            if (now - session.createdAt > maxAge) {
                this.batchSessions.delete(batchId);
            }
        }
    }
}

module.exports = new EnhancedBatchProcessor();
