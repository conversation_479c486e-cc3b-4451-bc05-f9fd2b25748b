const {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  ActionRow<PERSON><PERSON>er,
  <PERSON><PERSON>B<PERSON>er,
  ButtonStyle,
  ModalBuilder,
  TextInputBuilder,
  TextInputStyle,
  MessageFlags
} = require('discord.js');
const fs = require('fs');
const path = require('path');

const APPLICATION_CONFIG_FILE = path.join(__dirname, '..', 'application_config.json');
const FORM_SESSIONS_FILE = path.join(__dirname, '..', 'form_sessions.json');

class DynamicFormHandler {
  static loadConfig(guildId) {
    try {
      if (fs.existsSync(APPLICATION_CONFIG_FILE)) {
        const data = JSON.parse(fs.readFileSync(APPLICATION_CONFIG_FILE, 'utf8'));
        return data[guildId] || null;
      }
    } catch (error) {
      console.error('Error loading application config:', error);
    }
    return null;
  }

  static saveFormSession(guildId, userId, sessionD<PERSON>) {
    try {
      let data = {};
      if (fs.existsSync(FORM_SESSIONS_FILE)) {
        data = JSON.parse(fs.readFileSync(FORM_SESSIONS_FILE, 'utf8'));
      }
      
      if (!data[guildId]) {
        data[guildId] = {};
      }
      
      data[guildId][userId] = {
        ...sessionData,
        lastUpdated: new Date().toISOString()
      };
      
      fs.writeFileSync(FORM_SESSIONS_FILE, JSON.stringify(data, null, 2));
    } catch (error) {
      console.error('Error saving form session:', error);
    }
  }

  static getFormSession(guildId, userId) {
    try {
      if (fs.existsSync(FORM_SESSIONS_FILE)) {
        const data = JSON.parse(fs.readFileSync(FORM_SESSIONS_FILE, 'utf8'));
        return data[guildId]?.[userId] || null;
      }
    } catch (error) {
      console.error('Error loading form session:', error);
    }
    return null;
  }

  static clearFormSession(guildId, userId) {
    try {
      if (fs.existsSync(FORM_SESSIONS_FILE)) {
        const data = JSON.parse(fs.readFileSync(FORM_SESSIONS_FILE, 'utf8'));
        if (data[guildId]?.[userId]) {
          delete data[guildId][userId];
          fs.writeFileSync(FORM_SESSIONS_FILE, JSON.stringify(data, null, 2));
        }
      }
    } catch (error) {
      console.error('Error clearing form session:', error);
    }
  }

  static async startDynamicForm(interaction) {
    const config = this.loadConfig(interaction.guildId);
    
    if (!config || !config.enabled) {
      await interaction.reply({
        content: '❌ The application system is currently disabled.',
        flags: MessageFlags.Ephemeral
      });
      return;
    }

    if (config.questions.length === 0) {
      await interaction.reply({
        content: '❌ No application questions have been configured.',
        flags: MessageFlags.Ephemeral
      });
      return;
    }

    // Initialize form session
    const sessionData = {
      guildId: interaction.guildId,
      userId: interaction.user.id,
      username: interaction.user.tag,
      questions: config.questions,
      answers: {},
      verificationResults: {},
      currentQuestionIndex: 0,
      useDM: false,
      startTime: new Date().toISOString(),
      status: 'in_progress'
    };

    this.saveFormSession(interaction.guildId, interaction.user.id, sessionData);

    // Try to send form via DM first
    try {
      const dmChannel = await interaction.user.createDM();
      
      const welcomeEmbed = new EmbedBuilder()
        .setTitle('📋 Server Application Form')
        .setDescription(`Welcome to the ${interaction.guild.name} application process!`)
        .setColor(0x00AE86)
        .addFields([
          {
            name: '📝 Process Overview',
            value: `You will be asked ${config.questions.length} question${config.questions.length !== 1 ? 's' : ''} with real-time verification.`,
            inline: false
          },
          {
            name: '🔍 Verification',
            value: '',
            inline: false
          },
          {
            name: '⏱️ Estimated Time',
            value: '3-7 minutes',
            inline: true
          },
          {
            name: '🔒 Privacy',
            value: 'This conversation is private',
            inline: true
          }
        ])
        .setFooter({ text: 'Click "Start Form" to begin' });

      const startButton = new ActionRowBuilder()
        .addComponents(
          new ButtonBuilder()
            .setCustomId(`dynamic_form_start_${interaction.guildId}_${interaction.user.id}`)
            .setLabel('Start Form')
            .setStyle(ButtonStyle.Primary)
            .setEmoji('📝')
        );

      await dmChannel.send({
        embeds: [welcomeEmbed],
        components: [startButton]
      });

      // Update session to use DM
      sessionData.useDM = true;
      this.saveFormSession(interaction.guildId, interaction.user.id, sessionData);

      await interaction.reply({
        content: '✅ Application form sent to your DMs!',
        flags: MessageFlags.Ephemeral
      });

    } catch (error) {
      console.log('DMs are closed, using modal system');
      
      // DMs are closed, use modal system
      await this.startModalForm(interaction, sessionData);
    }
  }

  static async startModalForm(interaction, sessionData) {
    const embed = new EmbedBuilder()
      .setTitle('📋 Server Application Form')
      .setDescription('Your DMs appear to be closed, so we\'ll use an interactive form system here.')
      .setColor(0x00AE86)
      .addFields([
        {
          name: '📝 Process Overview',
          value: `You will be asked ${sessionData.questions.length} question${sessionData.questions.length !== 1 ? 's' : ''} with real-time verification.`,
          inline: false
        },
        {
          name: '🔍 Verification',
          value: '',
          inline: false
        },
        {
          name: '⚠️ Note',
          value: 'If there are many questions, they will be split across multiple forms.',
          inline: false
        }
      ])
      .setFooter({ text: 'Click "Start Form" to begin' });

    const startButton = new ActionRowBuilder()
      .addComponents(
        new ButtonBuilder()
          .setCustomId(`dynamic_form_start_${interaction.guildId}_${interaction.user.id}`)
          .setLabel('Start Form')
          .setStyle(ButtonStyle.Primary)
          .setEmoji('📝')
      );

    await interaction.reply({
      embeds: [embed],
      components: [startButton],
      flags: MessageFlags.Ephemeral
    });
  }

  static async handleFormStart(interaction) {
    const [, , , guildId, userId] = interaction.customId.split('_');
    const sessionData = this.getFormSession(guildId, userId);
    
    if (!sessionData) {
      await interaction.reply({
        content: '❌ Form session not found. Please start the application process again.',
        flags: MessageFlags.Ephemeral
      });
      return;
    }

    // Start with the first question
    await this.showNextQuestion(interaction, sessionData);
  }

  static async showNextQuestion(interaction, sessionData) {
    const currentIndex = sessionData.currentQuestionIndex;
    const question = sessionData.questions[currentIndex];
    
    if (!question) {
      // All questions completed, process the form
      await this.processCompletedForm(interaction, sessionData);
      return;
    }

    if (sessionData.useDM) {
      await this.showQuestionInDM(interaction, sessionData, question, currentIndex);
    } else {
      await this.showQuestionInModal(interaction, sessionData, question, currentIndex);
    }
  }

  static async showQuestionInDM(interaction, sessionData, question, questionIndex) {
    const dmChannel = await interaction.user.createDM();
    
    // Generate professional title based on question content
    const generateQuestionTitle = (questionText, index) => {
      const lowerQuestion = questionText.toLowerCase();
      
      if (lowerQuestion.includes('name')) {
        return `Step ${index + 1}: Your Full Name`;
      } else if (lowerQuestion.includes('email')) {
        return `Step ${index + 1}: Email Address`;
      } else if (lowerQuestion.includes('phone')) {
        return `Step ${index + 1}: Phone Number`;
      } else if (lowerQuestion.includes('group')) {
        return `Step ${index + 1}: Group Selection`;
      } else if (lowerQuestion.includes('age')) {
        return `Step ${index + 1}: Age Information`;
      } else if (lowerQuestion.includes('location') || lowerQuestion.includes('address')) {
        return `Step ${index + 1}: Location Details`;
      } else if (lowerQuestion.includes('experience')) {
        return `Step ${index + 1}: Experience Level`;
      } else if (lowerQuestion.includes('reason') || lowerQuestion.includes('why')) {
        return `Step ${index + 1}: Application Reason`;
      } else {
        return `Step ${index + 1}: Information Required`;
      }
    };

    // Generate professional description based on question type
    const generateQuestionDescription = (questionType) => {
      if (questionType === 'choice') {
        return 'Please select one of the available options.';
      } else {
        return 'Please provide your response below.';
      }
    };
    
    if (question.type === 'text') {
      // Text-based question in DM
      const embed = new EmbedBuilder()
        .setTitle(generateQuestionTitle(question.question, questionIndex))
        .setDescription(generateQuestionDescription(question.type))
        .setColor(0x3498DB);

      if (question.placeholder) {
        embed.addFields([
          {
            name: '💡 Example',
            value: question.placeholder,
            inline: false
          }
        ]);
      }

      await dmChannel.send({ embeds: [embed] });

    } else if (question.type === 'choice') {
      // Choice question in DM - show options as text
      const embed = new EmbedBuilder()
        .setTitle(generateQuestionTitle(question.question, questionIndex))
        .setDescription(generateQuestionDescription(question.type))
        .setColor(0x3498DB);

      // Add options as a field
      const optionsText = question.options.map((option, index) => 
        `**${index + 1}.** ${option}`
      ).join('\n');

      embed.addFields([
        {
          name: 'Available Options',
          value: optionsText,
          inline: false
        }
      ]);

      await dmChannel.send({ embeds: [embed] });
    }

    // Mark session as waiting for DM response
    sessionData.waitingForDMResponse = true;
    sessionData.currentQuestionIndex = questionIndex;
    this.saveFormSession(sessionData.guildId, sessionData.userId, sessionData);

    // Acknowledge the interaction to prevent "This interaction failed" error
    // Only for real button interactions, not mock interactions from DM messages
    if (!interaction.replied && !interaction.deferred && typeof interaction.deferUpdate === 'function') {
      await interaction.deferUpdate();
    }
  }

  static async showQuestionInModal(interaction, sessionData, question, questionIndex) {
    if (question.type === 'choice') {
      // For choice questions in server, show as buttons
      const embed = new EmbedBuilder()
        .setTitle(`📝 Question ${questionIndex + 1} of ${sessionData.questions.length}`)
        .setDescription(question.question)
        .setColor(0x3498DB)
        .addFields([
          {
            name: 'Instructions',
            value: 'Please select one of the options below.',
            inline: false
          },
          {
            name: 'Progress',
            value: `${questionIndex}/${sessionData.questions.length} questions completed`,
            inline: true
          }
        ]);

      const buttons = [];
      const maxButtonsPerRow = 5;
      const rows = [];
      
      for (let i = 0; i < question.options.length; i += maxButtonsPerRow) {
        const row = new ActionRowBuilder();
        const optionsSlice = question.options.slice(i, i + maxButtonsPerRow);
        
        optionsSlice.forEach((option, index) => {
          row.addComponents(
            new ButtonBuilder()
              .setCustomId(`dynamic_form_choice_${sessionData.guildId}_${sessionData.userId}_${questionIndex}_${i + index}`)
              .setLabel(option.length > 80 ? option.substring(0, 77) + '...' : option)
              .setStyle(ButtonStyle.Secondary)
              .setEmoji('📝')
          );
        });
        
        rows.push(row);
      }

      const method = interaction.replied || interaction.deferred ? 'editReply' : 'reply';
      await interaction[method]({
        embeds: [embed],
        components: rows,
        flags: MessageFlags.Ephemeral
      });

    } else {
      // For text questions, show modal
      const modal = new ModalBuilder()
        .setCustomId(`dynamic_form_modal_${sessionData.guildId}_${sessionData.userId}_${questionIndex}`)
        .setTitle(`Question ${questionIndex + 1} of ${sessionData.questions.length}`);

      const textInput = new TextInputBuilder()
        .setCustomId('answer')
        .setLabel(question.question)
        .setStyle(question.multiline ? TextInputStyle.Paragraph : TextInputStyle.Short)
        .setRequired(question.required)
        .setMaxLength(question.maxLength || 1000);

      if (question.placeholder) {
        textInput.setPlaceholder(question.placeholder);
      }

      const actionRow = new ActionRowBuilder().addComponents(textInput);
      modal.addComponents(actionRow);

      await interaction.showModal(modal);
    }
  }

  static async handleTextAnswer(interaction) {
    const [, , , guildId, userId, questionIndex] = interaction.customId.split('_');
    const sessionData = this.getFormSession(guildId, userId);
    
    if (!sessionData) {
      await interaction.reply({
        content: '❌ Form session not found.',
        flags: MessageFlags.Ephemeral
      });
      return;
    }

    const question = sessionData.questions[parseInt(questionIndex)];
    
    const modal = new ModalBuilder()
      .setCustomId(`dynamic_form_modal_${guildId}_${userId}_${questionIndex}`)
      .setTitle(`Question ${parseInt(questionIndex) + 1} of ${sessionData.questions.length}`);

    const textInput = new TextInputBuilder()
      .setCustomId('answer')
      .setLabel(question.question)
      .setStyle(question.multiline ? TextInputStyle.Paragraph : TextInputStyle.Short)
      .setRequired(question.required)
      .setMaxLength(question.maxLength || 1000);

    if (question.placeholder) {
      textInput.setPlaceholder(question.placeholder);
    }

    const actionRow = new ActionRowBuilder().addComponents(textInput);
    modal.addComponents(actionRow);

    await interaction.showModal(modal);
  }

  static async handleModalSubmit(interaction) {
    const [, , , guildId, userId, questionIndex] = interaction.customId.split('_');
    const sessionData = this.getFormSession(guildId, userId);
    
    if (!sessionData) {
      await interaction.reply({
        content: '❌ Form session not found.',
        flags: MessageFlags.Ephemeral
      });
      return;
    }

    const answer = interaction.fields.getTextInputValue('answer');
    await this.processAnswer(interaction, sessionData, parseInt(questionIndex), answer);
  }

  static async handleChoiceAnswer(interaction) {
    const parts = interaction.customId.split('_');
    const guildId = parts[3];
    const userId = parts[4];
    const questionIndex = parseInt(parts[5]);
    const optionIndex = parseInt(parts[6]);
    
    const sessionData = this.getFormSession(guildId, userId);
    
    if (!sessionData) {
      await interaction.reply({
        content: '❌ Form session not found.',
        flags: MessageFlags.Ephemeral
      });
      return;
    }

    const question = sessionData.questions[questionIndex];
    const answer = question.options[optionIndex];
    
    await this.processAnswer(interaction, sessionData, questionIndex, answer);
  }

  static async processAnswer(interaction, sessionData, questionIndex, answer) {
    const question = sessionData.questions[questionIndex];
    
    // Perform verification silently in the backend
    let verificationResult = { valid: true, message: 'No verification required' };
    
    if (question.verifyInChannels) {
      const ApplicationHandler = require('./applicationHandler');
      verificationResult = await ApplicationHandler.verifyUserDataInChannels(answer, question, interaction);
      
      if (!verificationResult.valid && question.verificationMode === 'required') {
        // Verification failed for required question - fail silently, handle at the end
        sessionData.answers[questionIndex] = answer;
        sessionData.verificationResults[questionIndex] = verificationResult;
        sessionData.currentQuestionIndex = questionIndex + 1;
        sessionData.waitingForDMResponse = false;
        this.saveFormSession(sessionData.guildId, sessionData.userId, sessionData);

        // Check if this is the final question
        const isLastQuestion = questionIndex === sessionData.questions.length - 1;
        await this.sendAnswerRecordedMessage(interaction, sessionData, isLastQuestion);
        
        // Continue to next question
        await this.showNextQuestion(interaction, sessionData);
        return;
      }
    }

    // Save answer and verification result silently
    sessionData.answers[questionIndex] = answer;
    sessionData.verificationResults[questionIndex] = verificationResult;
    sessionData.currentQuestionIndex = questionIndex + 1;
    sessionData.waitingForDMResponse = false;
    this.saveFormSession(sessionData.guildId, sessionData.userId, sessionData);

    // Check if this is the final question
    const isLastQuestion = questionIndex === sessionData.questions.length - 1;
    
    if (isLastQuestion) {
      // Don't send intermediate message - go straight to processing
      await this.showNextQuestion(interaction, sessionData);
    } else {
      // Send intermediate message for non-final questions
      await this.sendAnswerRecordedMessage(interaction, sessionData, false);
      // Continue to next question
      await this.showNextQuestion(interaction, sessionData);
    }
  }

  static async handleDMMessage(message) {
    // Only process DM messages from users (not bots)
    if (message.author.bot || message.channel.type !== 1) return;

    // Check if user has an active form session waiting for DM response
    const allSessions = this.getAllFormSessions();
    let userSession = null;
    let guildId = null;

    for (const [guild, users] of Object.entries(allSessions)) {
      if (users[message.author.id] && users[message.author.id].waitingForDMResponse) {
        userSession = users[message.author.id];
        guildId = guild;
        break;
      }
    }

    if (!userSession) return; // No active session

    const currentQuestion = userSession.questions[userSession.currentQuestionIndex];
    if (!currentQuestion) return;

    let answer = message.content.trim();

    // Handle choice questions - allow number selection or full text
    if (currentQuestion.type === 'choice') {
      const numberMatch = answer.match(/^(\d+)$/);
      if (numberMatch) {
        const optionIndex = parseInt(numberMatch[1]) - 1;
        if (optionIndex >= 0 && optionIndex < currentQuestion.options.length) {
          answer = currentQuestion.options[optionIndex];
        } else {
          await message.reply('❌ Invalid option number. Please try again.');
          return;
        }
      } else {
        // Check if the answer matches any of the options
        const matchedOption = currentQuestion.options.find(option => 
          option.toLowerCase() === answer.toLowerCase()
        );
        if (matchedOption) {
          answer = matchedOption;
        } else {
          await message.reply('❌ Invalid option. Please type the number or exact option text.');
          return;
        }
      }
    }

    // Validate required fields
    if (currentQuestion.required && !answer) {
      await message.reply('❌ This question is required. Please provide an answer.');
      return;
    }

    // Create a mock interaction object for processing
    const mockInteraction = {
      user: message.author,
      guild: message.client.guilds.cache.get(guildId),
      guildId: guildId,
      replied: false,
      deferred: false
    };

    // Process the answer
    await this.processAnswer(mockInteraction, userSession, userSession.currentQuestionIndex, answer);
  }

  static getAllFormSessions() {
    try {
      if (fs.existsSync(FORM_SESSIONS_FILE)) {
        return JSON.parse(fs.readFileSync(FORM_SESSIONS_FILE, 'utf8'));
      }
    } catch (error) {
      console.error('Error loading all form sessions:', error);
    }
    return {};
  }

  static async handleRetry(interaction) {
    const [, , , guildId, userId, questionIndex] = interaction.customId.split('_');
    const sessionData = this.getFormSession(guildId, userId);
    
    if (!sessionData) {
      await interaction.reply({
        content: '❌ Form session not found.',
        flags: MessageFlags.Ephemeral
      });
      return;
    }

    // Reset to the failed question
    sessionData.currentQuestionIndex = parseInt(questionIndex);
    this.saveFormSession(guildId, userId, sessionData);

    await this.showNextQuestion(interaction, sessionData);
  }

  static async processCompletedForm(interaction, sessionData) {
    const config = this.loadConfig(sessionData.guildId);
    
    // Check current queue load to determine processing strategy
    const currentQueueSize = this.getQueueSize();
    
    if (currentQueueSize === 0) {
      // Queue is empty - process immediately without queue delays
      console.log('🚀 Queue empty - processing form immediately');
      await this.processApplicationImmediately(interaction, sessionData, config);
    } else {
      // Queue has items - use standard queue processing
      console.log(`📋 Queue has ${currentQueueSize} items - using queue system`);
      await this.addToProcessingQueue(interaction, sessionData, config);
      await this.processApplicationFromQueue(interaction, sessionData, config);
    }
  }

  static getQueueSize() {
    try {
      const queueFile = path.join(__dirname, '..', 'application_queue.json');
      if (fs.existsSync(queueFile)) {
        const queue = JSON.parse(fs.readFileSync(queueFile, 'utf8'));
        // Count only queued items (not processed ones)
        return queue.filter(item => item.status === 'queued').length;
      }
    } catch (error) {
      console.error('Error getting queue size:', error);
    }
    return 0;
  }

  static async processApplicationImmediately(interaction, sessionData, config) {
    let responseSuccess = false;
    let assignedRoles = [];
    
    try {
      console.log('⚡ Processing application immediately - no queue delays');
      
      // Process verification silently in background
      const failedVerifications = [];
      for (const [index, result] of Object.entries(sessionData.verificationResults)) {
        const question = sessionData.questions[parseInt(index)];
        if (question.verifyInChannels && question.verificationMode === 'required' && !result.valid) {
          failedVerifications.push({
            question: question.question,
            answer: sessionData.answers[index],
            error: result.error
          });
        }
      }

      // Assign roles and log (if successful)
      if (failedVerifications.length === 0) {
        try {
          assignedRoles = await this.assignRoles(interaction, sessionData, config);
          await this.logCompletedForm(interaction, sessionData, assignedRoles, config);
        } catch (error) {
          console.error('Error in role assignment or logging:', error);
          // Continue to send response even if role assignment fails
        }
        
        // GUARANTEED RESPONSE: Send Form Submission embed (successful)
        await this.sendFormSubmissionEmbedWithRetry(interaction, sessionData);
        responseSuccess = true;
      } else {
        // GUARANTEED RESPONSE: Send Access Update embed (denied)
        await this.sendAccessUpdateEmbedWithRetry(interaction, sessionData, failedVerifications);
        responseSuccess = true;
      }
      
    } catch (error) {
      console.error('Critical error in processApplicationImmediately:', error);
      
      // FAIL-SAFE: If everything fails, send a basic response
      if (!responseSuccess) {
        await this.sendFailSafeResponse(interaction, sessionData);
      }
    } finally {
      // Always clear session - no queue to update since we processed immediately
      try {
        this.clearFormSession(sessionData.guildId, sessionData.userId);
        console.log('✅ Immediate processing complete - session cleared');
      } catch (error) {
        console.error('Error in cleanup:', error);
      }
    }
  }

  static async addToProcessingQueue(interaction, sessionData, config) {
    // Add application to queue
    const queueEntry = {
      guildId: sessionData.guildId,
      userId: sessionData.userId,
      username: sessionData.username,
      answers: sessionData.answers,
      verificationResults: sessionData.verificationResults,
      submittedAt: new Date().toISOString(),
      status: 'queued'
    };

    // Save to queue file
    this.saveToQueue(queueEntry);

    // Notify admins about new application in queue
    await this.notifyAdminsNewApplication(interaction, sessionData, config);
  }

  static saveToQueue(queueEntry) {
    try {
      const queueFile = path.join(__dirname, '..', 'application_queue.json');
      let queue = [];
      
      if (fs.existsSync(queueFile)) {
        queue = JSON.parse(fs.readFileSync(queueFile, 'utf8'));
      }
      
      queue.push(queueEntry);
      fs.writeFileSync(queueFile, JSON.stringify(queue, null, 2));
    } catch (error) {
      console.error('Error saving to queue:', error);
    }
  }

  static async notifyAdminsNewApplication(interaction, sessionData, config) {
    try {
      // Notify via admin channel if configured
      if (config.adminChannelId) {
        const adminChannel = await interaction.guild.channels.fetch(config.adminChannelId);
        if (adminChannel) {
          const notificationEmbed = new EmbedBuilder()
            .setDescription(`📥 New application received from ${sessionData.username}. Added to review queue.`)
            .setColor(0x3498DB)
            .setTimestamp();

          await adminChannel.send({ embeds: [notificationEmbed] });
        }
      }

      // Also notify via log channel if different from admin channel
      if (config.logChannelId && config.logChannelId !== config.adminChannelId) {
        const logChannel = await interaction.guild.channels.fetch(config.logChannelId);
        if (logChannel) {
          const logEmbed = new EmbedBuilder()
            .setDescription(`📥 New application received from ${sessionData.username}. Added to review queue.`)
            .setColor(0x3498DB)
            .setTimestamp();

          await logChannel.send({ embeds: [logEmbed] });
        }
      }
    } catch (error) {
      console.error('Error notifying admins:', error);
    }
  }

  static async processApplicationFromQueue(interaction, sessionData, config) {
    let responseSuccess = false;
    let assignedRoles = [];
    
    try {
      // Process verification silently in background
      const failedVerifications = [];
      for (const [index, result] of Object.entries(sessionData.verificationResults)) {
        const question = sessionData.questions[parseInt(index)];
        if (question.verifyInChannels && question.verificationMode === 'required' && !result.valid) {
          failedVerifications.push({
            question: question.question,
            answer: sessionData.answers[index],
            error: result.error
          });
        }
      }

      // Assign roles and log (if successful)
      if (failedVerifications.length === 0) {
        try {
          assignedRoles = await this.assignRoles(interaction, sessionData, config);
          await this.logCompletedForm(interaction, sessionData, assignedRoles, config);
        } catch (error) {
          console.error('Error in role assignment or logging:', error);
          // Continue to send response even if role assignment fails
        }
        
        // GUARANTEED RESPONSE: Send Form Submission embed (successful)
        await this.sendFormSubmissionEmbedWithRetry(interaction, sessionData);
        responseSuccess = true;
      } else {
        // GUARANTEED RESPONSE: Send Access Update embed (denied)
        await this.sendAccessUpdateEmbedWithRetry(interaction, sessionData, failedVerifications);
        responseSuccess = true;
      }
      
    } catch (error) {
      console.error('Critical error in processApplicationFromQueue:', error);
      
      // FAIL-SAFE: If everything fails, send a basic response
      if (!responseSuccess) {
        await this.sendFailSafeResponse(interaction, sessionData);
      }
    } finally {
      // Always update queue status and clear session
      try {
        this.updateQueueStatus(sessionData.guildId, sessionData.userId, assignedRoles.length > 0 ? 'approved' : 'processed');
        this.clearFormSession(sessionData.guildId, sessionData.userId);
      } catch (error) {
        console.error('Error in cleanup:', error);
      }
    }
  }

  static updateQueueStatus(guildId, userId, status) {
    try {
      const queueFile = path.join(__dirname, '..', 'application_queue.json');
      if (fs.existsSync(queueFile)) {
        let queue = JSON.parse(fs.readFileSync(queueFile, 'utf8'));
        
        // Find and update the application status
        const applicationIndex = queue.findIndex(app => app.guildId === guildId && app.userId === userId);
        if (applicationIndex !== -1) {
          queue[applicationIndex].status = status;
          queue[applicationIndex].processedAt = new Date().toISOString();
          fs.writeFileSync(queueFile, JSON.stringify(queue, null, 2));
        }
      }
    } catch (error) {
      console.error('Error updating queue status:', error);
    }
  }

  static async sendFailureMessage(interaction, sessionData, failedVerifications) {
    const failureMessage = '❌ Thank you for your application. Unfortunately, we cannot approve your request at this time. Please feel free to contact our staff if you have any questions.';

    if (sessionData.useDM) {
      const dmChannel = await interaction.user.createDM();
      await dmChannel.send({ content: failureMessage });
    } else {
      await interaction.followUp({
        content: failureMessage,
        flags: MessageFlags.Ephemeral
      });
    }
  }

  static async sendAnswerRecordedMessage(interaction, sessionData, isLastQuestion = false) {
    let embed;
    
    if (isLastQuestion) {
      embed = new EmbedBuilder()
        .setDescription('✅ Your application has been successfully submitted. Please wait while we review your responses.')
        .setColor(0x00AE86);
    } else {
      embed = new EmbedBuilder()
        .setDescription('✓ Answer recorded. Next question coming up...')
        .setColor(0x3498DB);
    }

    if (sessionData.useDM) {
      const dmChannel = await interaction.user.createDM();
      await dmChannel.send({ embeds: [embed] });
    } else {
      // For modal interactions, we need to handle this differently
      if (!interaction.replied && !interaction.deferred) {
        await interaction.reply({
          embeds: [embed],
          flags: MessageFlags.Ephemeral
        });
      } else {
        await interaction.followUp({
          embeds: [embed],
          flags: MessageFlags.Ephemeral
        });
      }
    }

    // No delay needed - proceed immediately to next question
  }

  static async sendAcknowledgmentMessage(interaction, sessionData) {
    const embed = new EmbedBuilder()
      .setDescription('📝 Thank you for completing the application! Your responses have been received and are being reviewed. You\'ll get a response shortly.')
      .setColor(0x00AE86);

    if (sessionData.useDM) {
      const dmChannel = await interaction.user.createDM();
      await dmChannel.send({ embeds: [embed] });
    } else {
      await interaction.followUp({
        embeds: [embed],
        flags: MessageFlags.Ephemeral
      });
    }

    // Process immediately - no artificial delays needed
  }

  static async sendFormSubmissionEmbed(interaction, sessionData) {
    const config = this.loadConfig(sessionData.guildId);
    
    // Get custom success message or use defaults
    const successMessage = config.messages?.success || {
      title: '✅ Your application has been accepted.',
      description: 'Thank you for completing the application process.',
      footer: null
    };

    // Replace placeholders
    const title = this.replacePlaceholders(successMessage.title, {
      username: interaction.user.displayName || interaction.user.username,
      form_data: this.formatFormData(sessionData)
    });
    
    const description = this.replacePlaceholders(successMessage.description, {
      username: interaction.user.displayName || interaction.user.username,
      form_data: this.formatFormData(sessionData)
    });

    const embed = new EmbedBuilder()
      .setTitle(title)
      .setColor(0x00AE86)
      .setDescription(description);

    if (successMessage.footer) {
      const footer = this.replacePlaceholders(successMessage.footer, {
        username: interaction.user.displayName || interaction.user.username,
        form_data: this.formatFormData(sessionData)
      });
      embed.setFooter({ text: footer });
    }

    if (sessionData.useDM) {
      const dmChannel = await interaction.user.createDM();
      await dmChannel.send({ embeds: [embed] });
    } else {
      // Handle different interaction states
      try {
        if (!interaction.replied && !interaction.deferred) {
          await interaction.reply({
            embeds: [embed],
            flags: MessageFlags.Ephemeral
          });
        } else if (interaction.deferred) {
          await interaction.editReply({
            embeds: [embed]
          });
        } else {
          await interaction.followUp({
            embeds: [embed],
            flags: MessageFlags.Ephemeral
          });
        }
      } catch (error) {
        console.error('Error sending form submission embed:', error);
        // Try alternative method
        try {
          const dmChannel = await interaction.user.createDM();
          await dmChannel.send({ embeds: [embed] });
        } catch (dmError) {
          console.error('Failed to send via DM as well:', dmError);
          throw error;
        }
      }
    }
  }

  static async sendFormSubmissionEmbedWithRetry(interaction, sessionData, maxRetries = 3) {
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        await this.sendFormSubmissionEmbed(interaction, sessionData);
        console.log(`✅ Form submission embed sent successfully on attempt ${attempt}`);
        return; // Success, exit retry loop
      } catch (error) {
        console.error(`❌ Attempt ${attempt} failed to send form submission embed:`, error);
        
        if (attempt === maxRetries) {
          // Final attempt failed, send fail-safe response
          console.error('🚨 All attempts failed, sending fail-safe response');
          await this.sendFailSafeResponse(interaction, sessionData);
        } else {
          // Wait before retry
          await new Promise(resolve => setTimeout(resolve, 1000 * attempt));
        }
      }
    }
  }

  static async sendAccessUpdateEmbed(interaction, sessionData, failedVerifications) {
    const config = this.loadConfig(sessionData.guildId);
    
    // Get custom denial message or use defaults
    const denialMessage = config.messages?.denial || {
      title: '❌ Verification failed. Please try again or contact staff for help.',
      description: 'Your application could not be processed at this time.',
      footer: null
    };

    // Prepare reason for denial
    const reason = failedVerifications.length > 0 
      ? 'Background verification could not be completed'
      : 'Application requirements not met';

    // Replace placeholders
    const title = this.replacePlaceholders(denialMessage.title, {
      username: interaction.user.displayName || interaction.user.username,
      reason: reason,
      form_data: this.formatFormData(sessionData)
    });
    
    const description = this.replacePlaceholders(denialMessage.description, {
      username: interaction.user.displayName || interaction.user.username,
      reason: reason,
      form_data: this.formatFormData(sessionData)
    });

    const embed = new EmbedBuilder()
      .setTitle(title)
      .setColor(0xE74C3C)
      .setDescription(description);

    if (denialMessage.footer) {
      const footer = this.replacePlaceholders(denialMessage.footer, {
        username: interaction.user.displayName || interaction.user.username,
        reason: reason,
        form_data: this.formatFormData(sessionData)
      });
      embed.setFooter({ text: footer });
    }

    if (sessionData.useDM) {
      const dmChannel = await interaction.user.createDM();
      await dmChannel.send({ embeds: [embed] });
    } else {
      // Handle different interaction states
      try {
        if (!interaction.replied && !interaction.deferred) {
          await interaction.reply({
            embeds: [embed],
            flags: MessageFlags.Ephemeral
          });
        } else if (interaction.deferred) {
          await interaction.editReply({
            embeds: [embed]
          });
        } else {
          await interaction.followUp({
            embeds: [embed],
            flags: MessageFlags.Ephemeral
          });
        }
      } catch (error) {
        console.error('Error sending access update embed:', error);
        // Try alternative method
        try {
          const dmChannel = await interaction.user.createDM();
          await dmChannel.send({ embeds: [embed] });
        } catch (dmError) {
          console.error('Failed to send via DM as well:', dmError);
          throw error;
        }
      }
    }
  }

  static async sendAccessUpdateEmbedWithRetry(interaction, sessionData, failedVerifications, maxRetries = 3) {
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        await this.sendAccessUpdateEmbed(interaction, sessionData, failedVerifications);
        console.log(`✅ Access update embed sent successfully on attempt ${attempt}`);
        return; // Success, exit retry loop
      } catch (error) {
        console.error(`❌ Attempt ${attempt} failed to send access update embed:`, error);
        
        if (attempt === maxRetries) {
          // Final attempt failed, send fail-safe response
          console.error('🚨 All attempts failed, sending fail-safe response');
          await this.sendFailSafeResponse(interaction, sessionData);
        } else {
          // Wait before retry
          await new Promise(resolve => setTimeout(resolve, 1000 * attempt));
        }
      }
    }
  }

  static async sendFailSafeResponse(interaction, sessionData) {
    console.log('🚨 FAIL-SAFE: Sending guaranteed response to user');
    
    const failSafeEmbed = new EmbedBuilder()
      .setTitle('📄 Application Received')
      .setDescription('Your application has been received and is being processed. You will be contacted if additional information is needed.')
      .setColor(0x3498DB);

    try {
      if (sessionData.useDM) {
        const dmChannel = await interaction.user.createDM();
        await dmChannel.send({ embeds: [failSafeEmbed] });
        console.log('✅ Fail-safe response sent via DM');
      } else {
        // Handle different interaction states for fail-safe
        try {
          if (!interaction.replied && !interaction.deferred) {
            await interaction.reply({
              embeds: [failSafeEmbed],
              flags: MessageFlags.Ephemeral
            });
          } else if (interaction.deferred) {
            await interaction.editReply({
              embeds: [failSafeEmbed]
            });
          } else {
            await interaction.followUp({
              embeds: [failSafeEmbed],
              flags: MessageFlags.Ephemeral
            });
          }
          console.log('✅ Fail-safe response sent via interaction');
        } catch (interactionError) {
          console.error('Error with interaction response, trying DM:', interactionError);
          // Try DM as fallback
          const dmChannel = await interaction.user.createDM();
          await dmChannel.send({ embeds: [failSafeEmbed] });
        }
      }
    } catch (error) {
      console.error('🚨 CRITICAL: Even fail-safe response failed:', error);
      
      // Last resort: try basic text message
      try {
        const basicMessage = 'Your application has been received and is being processed.';
        
        if (sessionData.useDM) {
          const dmChannel = await interaction.user.createDM();
          await dmChannel.send({ content: basicMessage });
        } else {
          await interaction.followUp({
            content: basicMessage,
            flags: MessageFlags.Ephemeral
          });
        }
        console.log('✅ Last resort basic message sent');
      } catch (finalError) {
        console.error('🚨 ABSOLUTE FAILURE: Could not send any response to user:', finalError);
        // Log this critical failure for manual intervention
        this.logCriticalFailure(sessionData, finalError);
      }
    }
  }

  static logCriticalFailure(sessionData, error) {
    try {
      const failureLog = {
        timestamp: new Date().toISOString(),
        userId: sessionData.userId,
        username: sessionData.username,
        guildId: sessionData.guildId,
        error: error.message,
        stack: error.stack,
        sessionData: sessionData
      };
      
      const logFile = path.join(__dirname, '..', 'critical_failures.json');
      let failures = [];
      
      if (fs.existsSync(logFile)) {
        failures = JSON.parse(fs.readFileSync(logFile, 'utf8'));
      }
      
      failures.push(failureLog);
      fs.writeFileSync(logFile, JSON.stringify(failures, null, 2));
      
      console.error('🚨 Critical failure logged to critical_failures.json');
    } catch (logError) {
      console.error('🚨 Could not even log the critical failure:', logError);
    }
  }

  static async sendUserSummary(interaction, sessionData, assignedRoles, approved) {
    const embed = new EmbedBuilder()
      .setTitle('📋 Application Summary')
      .setDescription('Here\'s a summary of your application:')
      .setColor(approved ? 0x00AE86 : 0xE74C3C)
      .addFields([
        {
          name: '📝 Your Responses',
          value: Object.entries(sessionData.answers)
            .map(([index, answer]) => {
              const question = sessionData.questions[parseInt(index)];
              return `**${question.question}**\n${answer}`;
            })
            .join('\n\n'),
          inline: false
        },
        {
          name: '📊 Application Status',
          value: approved ? '✅ Approved' : '❌ Not Approved',
          inline: true
        },
        {
          name: '⏰ Processed',
          value: new Date().toLocaleString(),
          inline: true
        }
      ]);

    if (approved && assignedRoles.length > 0) {
      embed.addFields([
        {
          name: '🎭 Roles Assigned',
          value: assignedRoles.map(roleId => `<@&${roleId}>`).join(', '),
          inline: false
        }
      ]);
    }

    embed.setFooter({ 
      text: approved 
        ? 'Welcome to the community! If you have any questions, feel free to ask our staff.' 
        : 'If you have questions about this decision, please contact our staff team.'
    });

    if (sessionData.useDM) {
      const dmChannel = await interaction.user.createDM();
      await dmChannel.send({ embeds: [embed] });
    } else {
      await interaction.followUp({
        embeds: [embed],
        flags: MessageFlags.Ephemeral
      });
    }
  }

  static async sendSuccessMessage(interaction, sessionData, assignedRoles) {
    // This method is now replaced by sendDecisionMessage and sendUserSummary
    // Keeping for backward compatibility if needed elsewhere
    const successMessage = '✅ Great news! Your application has been approved. Welcome to our community!';

    if (sessionData.useDM) {
      const dmChannel = await interaction.user.createDM();
      await dmChannel.send({ content: successMessage });
    } else {
      await interaction.followUp({
        content: successMessage,
        flags: MessageFlags.Ephemeral
      });
    }
  }

  static async assignRoles(interaction, sessionData, config) {
    const assignedRoles = [];
    
    try {
      const guild = interaction.guild;
      const member = await guild.members.fetch(sessionData.userId);
      
      for (const [questionIndex, answer] of Object.entries(sessionData.answers)) {
        const roleAssignments = config.roleAssignments[questionIndex];
        if (roleAssignments) {
          let roleId = null;

          // First try exact match (for choice questions and exact text matches)
          if (roleAssignments[answer]) {
            roleId = roleAssignments[answer];
          } else {
            // For text questions, try case-insensitive matching
            const normalizedAnswer = answer.toLowerCase().trim();
            roleId = roleAssignments[normalizedAnswer];
          }

          if (roleId) {
            try {
              const role = await guild.roles.fetch(roleId);
              if (role) {
                await member.roles.add(role, 'Automatic role assignment from verified dynamic form');
                assignedRoles.push(roleId);
                console.log(`Assigned role ${role.name} to user for answer: "${answer}"`);
              }
            } catch (error) {
              console.error(`Error assigning role ${roleId}:`, error);
            }
          }
        }
      }
    } catch (error) {
      console.error('Error in role assignment:', error);
    }
    
    return assignedRoles;
  }

  static async logCompletedForm(interaction, sessionData, assignedRoles, config) {
    if (!config.logChannelId) return;

    try {
      const logChannel = await interaction.guild.channels.fetch(config.logChannelId);
      if (!logChannel) return;

      const logEmbed = new EmbedBuilder()
        .setTitle('📋 Dynamic Form Submission')
        .setDescription('New application completed with real-time verification')
        .setColor(0x00AE86)
        .addFields([
          {
            name: '👤 User Information',
            value: `<@${sessionData.userId}> (${sessionData.username})\nID: ${sessionData.userId}`,
            inline: true
          },
          {
            name: '⏰ Submission Details',
            value: `Started: ${new Date(sessionData.startTime).toLocaleString()}\nCompleted: ${new Date().toLocaleString()}\nMethod: ${sessionData.useDM ? 'Direct Message' : 'Server Modal'}`,
            inline: true
          },
          {
            name: '🎭 Roles Assigned',
            value: assignedRoles.length > 0 
              ? assignedRoles.map(roleId => `<@&${roleId}>`).join(', ')
              : 'None',
            inline: false
          }
        ])
        .setTimestamp()
        .setFooter({ text: `Form ID: ${sessionData.guildId}-${sessionData.userId}` });

      // Add questions and answers
      const questionsAndAnswers = Object.entries(sessionData.answers)
        .map(([index, answer]) => {
          const question = sessionData.questions[parseInt(index)];
          const verification = sessionData.verificationResults[index];
          const verificationIcon = verification?.valid ? '✅' : '⚠️';
          return `**Q${parseInt(index) + 1}:** ${question.question}\n**A:** ${answer} ${verificationIcon}`;
        })
        .join('\n\n');

      if (questionsAndAnswers.length > 1024) {
        // Split into multiple fields if too long
        const chunks = this.chunkString(questionsAndAnswers, 1024);
        chunks.forEach((chunk, index) => {
          logEmbed.addFields([
            {
              name: `📝 Questions & Answers ${index > 0 ? `(Part ${index + 1})` : ''}`,
              value: chunk,
              inline: false
            }
          ]);
        });
      } else {
        logEmbed.addFields([
          {
            name: '📝 Questions & Answers',
            value: questionsAndAnswers,
            inline: false
          }
        ]);
      }

      // Add verification summary
      const verificationSummary = Object.entries(sessionData.verificationResults)
        .map(([index, result]) => {
          const question = sessionData.questions[parseInt(index)];
          if (question.verifyInChannels) {
            return `Q${parseInt(index) + 1}: ${result.valid ? '✅ Verified' : '⚠️ Warning'}`;
          }
          return `Q${parseInt(index) + 1}: No verification`;
        })
        .join('\n');

      logEmbed.addFields([
        {
          name: '🔍 Verification Summary',
          value: verificationSummary,
          inline: false
        }
      ]);

      // Add revoke button
      const revokeButton = new ActionRowBuilder()
        .addComponents(
          new ButtonBuilder()
            .setCustomId(`revoke_application_${sessionData.guildId}_${sessionData.userId}`)
            .setLabel('Revoke Access')
            .setStyle(ButtonStyle.Danger)
            .setEmoji('🚫')
        );

      const logMessage = await logChannel.send({
        embeds: [logEmbed],
        components: [revokeButton]
      });

      // Store log message ID for future reference
      sessionData.logMessageId = logMessage.id;
      
    } catch (error) {
      console.error('Error logging completed form:', error);
    }
  }

  static chunkString(str, length) {
    const chunks = [];
    let index = 0;
    while (index < str.length) {
      chunks.push(str.slice(index, index + length));
      index += length;
    }
    return chunks;
  }

  static async handleRevoke(interaction) {
    const [, , guildId, userId] = interaction.customId.split('_');
    
    const modal = new ModalBuilder()
      .setCustomId(`revoke_modal_${guildId}_${userId}`)
      .setTitle('Revoke User Access');

    const reasonInput = new TextInputBuilder()
      .setCustomId('reason')
      .setLabel('Revocation Reason')
      .setStyle(TextInputStyle.Paragraph)
      .setRequired(true)
      .setMaxLength(500)
      .setPlaceholder('Enter the reason for revoking this user\'s access...');

    const actionRow = new ActionRowBuilder().addComponents(reasonInput);
    modal.addComponents(actionRow);

    await interaction.showModal(modal);
  }

  static async processRevoke(interaction) {
    const [, , guildId, userId] = interaction.customId.split('_');
    const reason = interaction.fields.getTextInputValue('reason');
    
    try {
      const guild = interaction.guild;
      const member = await guild.members.fetch(userId);
      const config = this.loadConfig(guildId);
      
      // Remove all roles that were assigned through the application system
      const rolesToRemove = [];
      for (const roleAssignments of Object.values(config.roleAssignments || {})) {
        for (const roleId of Object.values(roleAssignments)) {
          if (member.roles.cache.has(roleId)) {
            rolesToRemove.push(roleId);
          }
        }
      }

      // Remove roles
      for (const roleId of rolesToRemove) {
        try {
          const role = await guild.roles.fetch(roleId);
          if (role) {
            await member.roles.remove(role, `Access revoked by ${interaction.user.tag}: ${reason}`);
          }
        } catch (error) {
          console.error(`Error removing role ${roleId}:`, error);
        }
      }

      // Update the log embed
      const originalEmbed = interaction.message.embeds[0];
      const updatedEmbed = new EmbedBuilder(originalEmbed.toJSON())
        .setColor(0xE74C3C)
        .setTitle('🚫 Dynamic Form Submission - REVOKED')
        .addFields([
          {
            name: '🚫 Revocation Details',
            value: `**Revoked by:** <@${interaction.user.id}> (${interaction.user.tag})\n**Reason:** ${reason}\n**Revoked at:** ${new Date().toLocaleString()}`,
            inline: false
          },
          {
            name: '🎭 Roles Removed',
            value: rolesToRemove.length > 0 
              ? rolesToRemove.map(roleId => `<@&${roleId}>`).join(', ')
              : 'None',
            inline: false
          }
        ]);

      await interaction.update({
        embeds: [updatedEmbed],
        components: [] // Remove the revoke button
      });

      // Notify the user if possible
      try {
        const dmChannel = await member.user.createDM();
        
        // Get custom revoke message or use defaults
        const revokeMessage = config.messages?.revoke || {
          title: '🚫 Access Revoked',
          description: 'Your access to {server_name} has been revoked.',
          footer: 'If you believe this was done in error, please contact the server staff.'
        };

        // Replace placeholders
        const title = this.replacePlaceholders(revokeMessage.title, {
          username: member.user.displayName || member.user.username,
          server_name: guild.name,
          reason: reason
        });
        
        const description = this.replacePlaceholders(revokeMessage.description, {
          username: member.user.displayName || member.user.username,
          server_name: guild.name,
          reason: reason
        });

        const notificationEmbed = new EmbedBuilder()
          .setTitle(title)
          .setDescription(description)
          .setColor(0xE74C3C)
          .addFields([
            {
              name: '📝 Reason',
              value: reason,
              inline: false
            }
          ])
          .setTimestamp();

        if (revokeMessage.footer) {
          const footer = this.replacePlaceholders(revokeMessage.footer, {
            username: member.user.displayName || member.user.username,
            server_name: guild.name,
            reason: reason
          });
          notificationEmbed.setFooter({ text: footer });
        }

        await dmChannel.send({ embeds: [notificationEmbed] });
      } catch (error) {
        console.log('Could not send DM notification to revoked user');
      }

    } catch (error) {
      console.error('Error processing revoke:', error);
      await interaction.reply({
        content: '❌ An error occurred while processing the revocation.',
        flags: MessageFlags.Ephemeral
      });
    }
  }

  static replacePlaceholders(text, placeholders) {
    if (!text) return text;
    
    let result = text;
    Object.entries(placeholders).forEach(([key, value]) => {
      const placeholder = `{${key}}`;
      result = result.replace(new RegExp(placeholder.replace(/[{}]/g, '\\$&'), 'g'), value || '');
    });
    
    return result;
  }

  static formatFormData(sessionData) {
    if (!sessionData.answers || !sessionData.questions) return 'No form data available';
    
    return Object.entries(sessionData.answers)
      .map(([index, answer]) => {
        const question = sessionData.questions[parseInt(index)];
        return `**${question.question}**\n${answer}`;
      })
      .join('\n\n');
  }
}

module.exports = DynamicFormHandler;